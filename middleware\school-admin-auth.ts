// School admin authentication middleware
// Created: 2025-07-14
// Purpose: Ensure only school admins can access school admin dashboard

import { navigateTo, defineNuxtRouteMiddleware } from "#app"

export default defineNuxtRouteMiddleware(async (to, from) => {
  // This middleware is specifically for school admin routes
  // It should only run on school admin dashboard routes (e.g., /[schoolcode])
  
  // Skip for non-school admin routes
  if (!to.params.schoolcode) {
    return
  }

  const schoolCode = to.params.schoolcode as string
  const supabase = useSupabaseClient()

  try {
    // Check if user is authenticated
    const { data: { session } } = await supabase.auth.getSession()
    
    if (!session) {
      // Redirect to login page
      return navigateTo('/login')
    }

    // Validate school admin access
    try {
      const response = await $fetch('/api/schools/validate-admin-access', {
        method: 'POST',
        body: {
          schoolCode
        },
        headers: {
          'Authorization': `Bearer ${session.access_token}`
        }
      }) as any

      if (!response.success || !response.hasAccess) {
        // User doesn't have admin access to this school
        console.error('Access denied to school admin dashboard:', response)
        return navigateTo('/')
      }
    } catch (error) {
      console.error('Error validating school admin access:', error)
      return navigateTo('/')
    }
    
  } catch (error) {
    console.error('Error in school admin auth middleware:', error)
    return navigateTo('/login')
  }
})
