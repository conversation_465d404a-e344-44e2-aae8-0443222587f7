<template>
    <div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div class="sm:mx-auto sm:w-full sm:max-w-md">
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                Sign In to {{ schoolName }}
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600">
                Welcome to your school portal
            </p>
        </div>

        <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
            <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
                <form class="space-y-6" @submit.prevent="handleEmailLogin">
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700">
                            Email address
                        </label>
                        <div class="mt-1">
                            <input id="email" v-model="email" name="email" type="email" autocomplete="email" required
                                class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                placeholder="Enter your email" />
                        </div>
                    </div>

                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700">
                            Password
                        </label>
                        <div class="mt-1">
                            <input id="password" v-model="password" name="password" type="password"
                                autocomplete="current-password" required
                                class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                placeholder="Enter your password" />
                        </div>
                    </div>

                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <input id="remember-me" name="remember-me" type="checkbox"
                                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" />
                            <label for="remember-me" class="ml-2 block text-sm text-gray-900">
                                Remember me
                            </label>
                        </div>

                        <div class="text-sm">
                            <a href="#" class="font-medium text-blue-600 hover:text-blue-500">
                                Forgot your password?
                            </a>
                        </div>
                    </div>

                    <div>
                        <button type="submit" :disabled="emailLoading"
                            class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50">
                            <span v-if="!emailLoading">Sign in</span>
                            <span v-else>Signing in...</span>
                        </button>
                    </div>

                    <!-- OR Divider -->
                    <div class="mt-6">
                        <div class="relative">
                            <div class="absolute inset-0 flex items-center">
                                <div class="w-full border-t border-gray-300" />
                            </div>
                            <div class="relative flex justify-center text-sm">
                                <span class="px-2 bg-white text-gray-500">Or continue with</span>
                            </div>
                        </div>
                    </div>

                    <!-- Google Sign In -->
                    <div class="mt-6">
                        <button type="button" @click="handleSimpleGoogleLogin" :disabled="googleLoading"
                            class="w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50">
                            <svg class="w-5 h-5" viewBox="0 0 24 24">
                                <path fill="#4285F4"
                                    d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" />
                                <path fill="#34A853"
                                    d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" />
                                <path fill="#FBBC05"
                                    d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" />
                                <path fill="#EA4335"
                                    d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" />
                            </svg>
                            <span class="ml-2">
                                <span v-if="!googleLoading">Sign in with Google</span>
                                <span v-else>Signing in with Google...</span>
                            </span>
                        </button>
                    </div>
                </form>

                <!-- Error Message -->
                <div v-if="errorMsg" class="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
                    <div class="text-sm text-red-800">{{ errorMsg }}</div>
                </div>

                <!-- Success Message -->
                <div v-if="successMsg" class="mt-4 p-3 bg-green-50 border border-green-200 rounded-md">
                    <div class="text-sm text-green-800">{{ successMsg }}</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
const supabase = useSupabaseClient();
const router = useRouter();

// Get school code from subdomain
const schoolCode = ref('');
const schoolName = ref('School');

const email = ref('');
const password = ref('');
const emailLoading = ref(false);
const googleLoading = ref(false);
const errorMsg = ref('');
const successMsg = ref('');

onMounted(() => {
    if (process.client) {
        const host = window.location.host;
        const extractedCode = host.split('.')[0];

        if (extractedCode && extractedCode !== 'localhost') {
            schoolCode.value = extractedCode;
            schoolName.value = extractedCode.toUpperCase();
        }

        console.log('[simple-login] School:', schoolCode.value);
    }
});

const handleEmailLogin = async () => {
    emailLoading.value = true;
    errorMsg.value = '';

    try {
        const { data, error } = await supabase.auth.signInWithPassword({
            email: email.value,
            password: password.value,
        });

        if (error) throw error;

        if (data.user) {
            successMsg.value = 'Successfully signed in! Redirecting...';

            setTimeout(() => {
                // Simple redirect to dashboard on same domain
                router.push('/dashboard');
            }, 1000);
        }
    } catch (error: any) {
        console.error('[simple-login] Email login error:', error);
        errorMsg.value = error.message;
    } finally {
        emailLoading.value = false;
    }
};

const handleSimpleGoogleLogin = async () => {
    googleLoading.value = true;
    errorMsg.value = '';

    try {
        console.log('[simple-login] Starting simple Google OAuth...');
        console.log('[simple-login] Current domain:', window.location.origin);
        console.log('[simple-login] School code:', schoolCode.value);

        // Use current domain for redirect - wildcards in Supabase handle all subdomains!
        const redirectTo = `${window.location.origin}/auth/callback`;
        console.log('[simple-login] Redirect URL:', redirectTo);

        const { error } = await supabase.auth.signInWithOAuth({
            provider: 'google',
            options: {
                redirectTo: redirectTo,
                queryParams: {
                    prompt: 'select_account',
                }
            },
        });

        if (error) {
            throw error;
        }

        console.log('[simple-login] Google OAuth initiated successfully');
        // User will be redirected to Google, then back to same subdomain callback

    } catch (error: any) {
        console.error('[simple-login] Google OAuth error:', error);
        errorMsg.value = `Google sign-in failed: ${error.message}`;
        googleLoading.value = false;
    }
};
</script>
