// Google OAuth Configuration Checker
// Run this in console to check your Google OAuth setup

async function checkGoogleOAuthConfig() {
    console.log('=== Google OAuth Configuration Check ===\n');
    
    // Current environment
    console.log('1. Current Environment:');
    console.log(`   URL: ${window.location.href}`);
    console.log(`   Host: ${window.location.host}`);
    console.log(`   Protocol: ${window.location.protocol}`);
    
    // What we're trying to do
    console.log('\n2. OAuth Flow Analysis:');
    console.log('   Source: xba1224.localhost:3000/oauth-test');
    console.log('   Redirect: http://localhost:3000/auth/callback');
    console.log('   Issue: 400 Bad Request on token exchange');
    
    // Supabase URL being called
    console.log('\n3. Supabase OAuth Endpoint:');
    console.log('   URL: https://nhgyywlfopodxomxbegx.supabase.co/auth/v1/token');
    console.log('   Method: POST');
    console.log('   Grant Type: pkce');
    console.log('   Status: 400 Bad Request ❌');
    
    // Required Google Cloud Console Configuration
    console.log('\n4. Required Google Cloud Console Setup:');
    console.log('   Project: Your Google Cloud Project');
    console.log('   OAuth 2.0 Client ID: [NEED TO CHECK]');
    console.log('   Authorized redirect URIs:');
    console.log('   ✓ https://nhgyywlfopodxomxbegx.supabase.co/auth/v1/callback');
    console.log('   ✓ http://localhost:3000/auth/callback (for testing)');
    
    // Supabase Configuration
    console.log('\n5. Required Supabase Configuration:');
    console.log('   Dashboard: Authentication → Providers → Google');
    console.log('   ✓ Google OAuth enabled');
    console.log('   ✓ Client ID from Google Cloud Console');
    console.log('   ✓ Client Secret from Google Cloud Console');
    console.log('   ✓ Redirect URL: http://localhost:3000/auth/callback');
    
    // Most likely issues
    console.log('\n6. Most Likely Issues:');
    console.log('   🔍 Google OAuth Client not configured for localhost');
    console.log('   🔍 Wrong redirect URI in Google Cloud Console');
    console.log('   🔍 Supabase Google provider not properly configured');
    console.log('   🔍 Client ID/Secret mismatch between Google and Supabase');
    
    // Next steps
    console.log('\n7. Next Steps to Fix:');
    console.log('   1. Check Google Cloud Console → APIs & Services → Credentials');
    console.log('   2. Find your OAuth 2.0 Client ID');
    console.log('   3. Add http://localhost:3000/* to authorized origins');
    console.log('   4. Add https://nhgyywlfopodxomxbegx.supabase.co/auth/v1/callback to redirect URIs');
    console.log('   5. Copy Client ID and Secret to Supabase → Authentication → Providers → Google');
    console.log('   6. Enable Google provider in Supabase');
    
    console.log('\n=== Check Complete ===');
}

// Run the check
checkGoogleOAuthConfig();
