<template>
    <div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div class="sm:mx-auto sm:w-full sm:max-w-md">
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                Manual Google OAuth Test
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600">
                Direct Google OAuth without Supabase
            </p>
        </div>

        <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
            <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">

                <!-- Current Info -->
                <div class="mb-6 p-4 bg-gray-100 rounded-lg">
                    <h3 class="text-sm font-medium text-gray-900 mb-2">Current Environment:</h3>
                    <div class="text-xs text-gray-600 space-y-1">
                        <div>Host: {{ currentHost }}</div>
                        <div>School Code: {{ schoolCode }}</div>
                        <div>Stored Code: {{ storedSchoolCode }}</div>
                    </div>
                </div>

                <!-- Manual OAuth Button -->
                <div class="space-y-4">
                    <button @click="startManualGoogleOAuth" :disabled="loading"
                        class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50">
                        <span v-if="!loading">Manual Google OAuth</span>
                        <span v-else>Starting OAuth...</span>
                    </button>

                    <button @click="clearStorage"
                        class="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Clear localStorage
                    </button>
                </div>

                <!-- Messages -->
                <div v-if="message" class="mt-4 p-3 rounded-md" :class="messageClass">
                    <div class="text-sm">{{ message }}</div>
                </div>

                <!-- Instructions -->
                <div class="mt-6 p-4 bg-green-50 rounded-lg">
                    <h3 class="text-sm font-medium text-green-900 mb-2">Manual OAuth Flow:</h3>
                    <ol class="text-xs text-green-800 space-y-1 list-decimal list-inside">
                        <li>This bypasses Supabase OAuth completely</li>
                        <li>Uses direct Google OAuth with our own callback</li>
                        <li>Manually creates Supabase session after OAuth</li>
                        <li>Should avoid the 400 Bad Request error</li>
                    </ol>
                </div>

                <!-- Debug Info -->
                <div class="mt-4 p-3 bg-yellow-50 rounded-lg">
                    <h3 class="text-sm font-medium text-yellow-900 mb-2">Debug Info:</h3>
                    <div class="text-xs text-yellow-800 space-y-1">
                        <div>Google Client ID needed for manual OAuth</div>
                        <div>Will need to get this from your Google Cloud Console</div>
                        <div>OAuth callback: /auth/manual-callback</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
const currentHost = ref('');
const schoolCode = ref('');
const storedSchoolCode = ref('');
const loading = ref(false);
const message = ref('');
const messageType = ref<'success' | 'error' | 'info'>('info');

onMounted(() => {
    if (process.client) {
        currentHost.value = window.location.host;
        schoolCode.value = window.location.host.split('.')[0];
        storedSchoolCode.value = localStorage.getItem('oauth_school_code') || 'None';

        console.log('[manual-oauth] Page loaded');
        console.log('[manual-oauth] Host:', currentHost.value);
        console.log('[manual-oauth] School code:', schoolCode.value);

        // Auto-refresh stored school code display
        const intervalId = setInterval(() => {
            storedSchoolCode.value = localStorage.getItem('oauth_school_code') || 'None';
        }, 1000);

        onUnmounted(() => {
            clearInterval(intervalId);
        });
    }
});

const messageClass = computed(() => {
    switch (messageType.value) {
        case 'success': return 'bg-green-50 border border-green-200 text-green-800';
        case 'error': return 'bg-red-50 border border-red-200 text-red-800';
        default: return 'bg-blue-50 border border-blue-200 text-blue-800';
    }
});

const startManualGoogleOAuth = async () => {
    if (!process.client) return;

    loading.value = true;
    message.value = '';

    try {
        const currentHost = window.location.host;
        const extractedSchoolCode = currentHost.split('.')[0];

        console.log('[manual-oauth] Starting manual OAuth...');
        console.log('[manual-oauth] School code:', extractedSchoolCode);

        // Store school code
        if (extractedSchoolCode && extractedSchoolCode !== 'localhost') {
            localStorage.setItem('oauth_school_code', extractedSchoolCode);
            console.log('[manual-oauth] Stored school code:', extractedSchoolCode);

            message.value = `School code "${extractedSchoolCode}" stored. Creating manual OAuth URL...`;
            messageType.value = 'success';
        } else {
            message.value = 'Warning: No valid school code detected.';
            messageType.value = 'error';
            loading.value = false;
            return;
        }

        // TODO: We need the Google Client ID from your Google Cloud Console
        // For now, let's show what we need
        message.value = 'Manual OAuth requires Google Client ID. Check console for next steps.';
        messageType.value = 'info';

        console.log('=== Manual OAuth Setup Required ===');
        console.log('1. Go to Google Cloud Console');
        console.log('2. Get your OAuth 2.0 Client ID');
        console.log('3. Add redirect URI: http://localhost:3000/auth/manual-callback');
        console.log('4. Update this component with your Client ID');
        console.log('=====================================');

        loading.value = false;

    } catch (error: any) {
        console.error('[manual-oauth] Error:', error);
        message.value = `Error: ${error.message}`;
        messageType.value = 'error';
        loading.value = false;
    }
};

const clearStorage = () => {
    if (!process.client) return;

    localStorage.removeItem('oauth_school_code');
    storedSchoolCode.value = 'None';
    message.value = 'localStorage cleared';
    messageType.value = 'info';
    console.log('[manual-oauth] localStorage cleared');
};
</script>
