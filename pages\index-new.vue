<template>
  <div>
    <!-- Hero Section -->
    <section class="bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-blue-900 py-20">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
          <h1 class="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6">
            Streamline Your
            <span class="text-blue-600 dark:text-blue-400">Teaching Journey</span>
          </h1>
          <p class="text-xl md:text-2xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto">
            Comprehensive educational platform for lesson planning, progress tracking, and enhanced teaching
            experiences.
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <NuxtLink to="/pricing"
              class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-colors">
              Start Free Trial
            </NuxtLink>
            <NuxtLink to="/demo"
              class="bg-white hover:bg-gray-50 text-blue-600 border-2 border-blue-600 px-8 py-4 rounded-lg text-lg font-semibold transition-colors dark:bg-gray-800 dark:text-blue-400 dark:border-blue-400 dark:hover:bg-gray-700">
              View Demo
            </NuxtLink>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="py-20 bg-white dark:bg-gray-900">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Everything You Need to Excel
          </h2>
          <p class="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            Powerful tools designed specifically for educators to enhance teaching effectiveness and student engagement.
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <!-- Feature 1 -->
          <div class="text-center p-6">
            <div
              class="bg-blue-100 dark:bg-blue-900 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <Icon name="heroicons:document-text" class="h-8 w-8 text-blue-600 dark:text-blue-400" />
            </div>
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">Lesson Planning</h3>
            <p class="text-gray-600 dark:text-gray-300">
              Create comprehensive lesson plans with templates, objectives, and detailed reflections.
            </p>
          </div>

          <!-- Feature 2 -->
          <div class="text-center p-6">
            <div
              class="bg-green-100 dark:bg-green-900 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <Icon name="heroicons:calendar-days" class="h-8 w-8 text-green-600 dark:text-green-400" />
            </div>
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">Schedule Management</h3>
            <p class="text-gray-600 dark:text-gray-300">
              Organize your teaching schedule, timetables, and academic calendar efficiently.
            </p>
          </div>

          <!-- Feature 3 -->
          <div class="text-center p-6">
            <div
              class="bg-purple-100 dark:bg-purple-900 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <Icon name="heroicons:chart-bar" class="h-8 w-8 text-purple-600 dark:text-purple-400" />
            </div>
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">Progress Tracking</h3>
            <p class="text-gray-600 dark:text-gray-300">
              Monitor student progress and teaching effectiveness with detailed analytics.
            </p>
          </div>

          <!-- Feature 4 -->
          <div class="text-center p-6">
            <div
              class="bg-orange-100 dark:bg-orange-900 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <Icon name="heroicons:users" class="h-8 w-8 text-orange-600 dark:text-orange-400" />
            </div>
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">School Management</h3>
            <p class="text-gray-600 dark:text-gray-300">
              Multi-school support with role-based access for administrators and teachers.
            </p>
          </div>

          <!-- Feature 5 -->
          <div class="text-center p-6">
            <div
              class="bg-red-100 dark:bg-red-900 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <Icon name="heroicons:document-arrow-up" class="h-8 w-8 text-red-600 dark:text-red-400" />
            </div>
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">Document Management</h3>
            <p class="text-gray-600 dark:text-gray-300">
              Store and organize teaching materials, DSKP, RPT, and academic documents.
            </p>
          </div>

          <!-- Feature 6 -->
          <div class="text-center p-6">
            <div
              class="bg-teal-100 dark:bg-teal-900 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <Icon name="heroicons:cog-6-tooth" class="h-8 w-8 text-teal-600 dark:text-teal-400" />
            </div>
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">Customizable Templates</h3>
            <p class="text-gray-600 dark:text-gray-300">
              Create and customize reflection templates to match your teaching style.
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- Pricing Section -->
    <section class="py-20 bg-gray-50 dark:bg-gray-800">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Simple, Transparent Pricing
          </h2>
          <p class="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            Choose the plan that works best for your school. Start with a free trial.
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-5xl mx-auto">
          <!-- Free Trial -->
          <div class="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-8 border-2 border-gray-200 dark:border-gray-700">
            <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">Free Trial</h3>
            <div class="text-4xl font-bold text-gray-900 dark:text-white mb-6">
              Free
              <span class="text-lg font-normal text-gray-600 dark:text-gray-300">for 30 days</span>
            </div>
            <ul class="space-y-3 mb-8">
              <li class="flex items-center">
                <Icon name="heroicons:check" class="h-5 w-5 text-green-500 mr-3" />
                <span class="text-gray-600 dark:text-gray-300">Up to 10 teachers</span>
              </li>
              <li class="flex items-center">
                <Icon name="heroicons:check" class="h-5 w-5 text-green-500 mr-3" />
                <span class="text-gray-600 dark:text-gray-300">All core features</span>
              </li>
              <li class="flex items-center">
                <Icon name="heroicons:check" class="h-5 w-5 text-green-500 mr-3" />
                <span class="text-gray-600 dark:text-gray-300">Email support</span>
              </li>
            </ul>
            <NuxtLink to="/pricing"
              class="w-full bg-gray-200 hover:bg-gray-300 text-gray-900 py-3 px-6 rounded-lg font-semibold transition-colors block text-center">
              Start Free Trial
            </NuxtLink>
          </div>

          <!-- Professional -->
          <div class="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-8 border-2 border-blue-500 relative">
            <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
              <span class="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-semibold">Most Popular</span>
            </div>
            <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">Professional</h3>
            <div class="text-4xl font-bold text-gray-900 dark:text-white mb-6">
              $29
              <span class="text-lg font-normal text-gray-600 dark:text-gray-300">per month</span>
            </div>
            <ul class="space-y-3 mb-8">
              <li class="flex items-center">
                <Icon name="heroicons:check" class="h-5 w-5 text-green-500 mr-3" />
                <span class="text-gray-600 dark:text-gray-300">Unlimited teachers</span>
              </li>
              <li class="flex items-center">
                <Icon name="heroicons:check" class="h-5 w-5 text-green-500 mr-3" />
                <span class="text-gray-600 dark:text-gray-300">Advanced analytics</span>
              </li>
              <li class="flex items-center">
                <Icon name="heroicons:check" class="h-5 w-5 text-green-500 mr-3" />
                <span class="text-gray-600 dark:text-gray-300">Priority support</span>
              </li>
              <li class="flex items-center">
                <Icon name="heroicons:check" class="h-5 w-5 text-green-500 mr-3" />
                <span class="text-gray-600 dark:text-gray-300">Custom templates</span>
              </li>
            </ul>
            <NuxtLink to="/pricing"
              class="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-6 rounded-lg font-semibold transition-colors block text-center">
              Get Started
            </NuxtLink>
          </div>

          <!-- Enterprise -->
          <div class="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-8 border-2 border-gray-200 dark:border-gray-700">
            <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">Enterprise</h3>
            <div class="text-4xl font-bold text-gray-900 dark:text-white mb-6">
              Custom
              <span class="text-lg font-normal text-gray-600 dark:text-gray-300">pricing</span>
            </div>
            <ul class="space-y-3 mb-8">
              <li class="flex items-center">
                <Icon name="heroicons:check" class="h-5 w-5 text-green-500 mr-3" />
                <span class="text-gray-600 dark:text-gray-300">Multiple schools</span>
              </li>
              <li class="flex items-center">
                <Icon name="heroicons:check" class="h-5 w-5 text-green-500 mr-3" />
                <span class="text-gray-600 dark:text-gray-300">Advanced integrations</span>
              </li>
              <li class="flex items-center">
                <Icon name="heroicons:check" class="h-5 w-5 text-green-500 mr-3" />
                <span class="text-gray-600 dark:text-gray-300">Dedicated support</span>
              </li>
              <li class="flex items-center">
                <Icon name="heroicons:check" class="h-5 w-5 text-green-500 mr-3" />
                <span class="text-gray-600 dark:text-gray-300">Custom training</span>
              </li>
            </ul>
            <NuxtLink to="/contact"
              class="w-full bg-gray-200 hover:bg-gray-300 text-gray-900 py-3 px-6 rounded-lg font-semibold transition-colors block text-center">
              Contact Sales
            </NuxtLink>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="py-20 bg-blue-600 dark:bg-blue-800">
      <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl md:text-4xl font-bold text-white mb-4">
          Ready to Transform Your Teaching?
        </h2>
        <p class="text-xl text-blue-100 mb-8">
          Join thousands of educators who are already using RPHMate to enhance their teaching experience.
        </p>
        <NuxtLink to="/pricing"
          class="bg-white hover:bg-gray-100 text-blue-600 px-8 py-4 rounded-lg text-lg font-semibold transition-colors inline-block">
          Start Your Free Trial Today
        </NuxtLink>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
// Use landing layout
definePageMeta({
  layout: 'landing' as any
})

// SEO
useHead({
  title: 'RPHMate - Streamline Your Teaching Journey',
  meta: [
    {
      name: 'description',
      content: 'Comprehensive educational platform for lesson planning, progress tracking, and enhanced teaching experiences. Start your free trial today.'
    }
  ]
})
</script>

<style scoped>
/* Custom styles for landing page */
</style>
