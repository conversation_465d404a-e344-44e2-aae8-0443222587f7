<template>
    <div>
        <div
            class="fixed inset-0 flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 p-2 sm:p-4 md:p-6 overflow-auto">
            <Card class="w-full max-w-sm sm:max-w-md md:max-w-2xl lg:max-w-4xl overflow-hidden shadow-2xl"
                variant="shadow-2xl" no-default-spacing>
                <div class="flex flex-col lg:flex-row min-h-[400px] sm:min-h-[500px]">
                    <!-- Left Panel - Splash/Animation -->
                    <div
                        class="lg:w-1/2 bg-gradient-to-br from-primary to-blue-600 flex items-center justify-center p-4 sm:p-6 lg:p-8 relative overflow-hidden">
                        <!-- Background Pattern -->
                        <div class="absolute inset-0 opacity-10">
                            <div class="absolute top-10 left-10 w-20 h-20 bg-white rounded-full animate-pulse"></div>
                            <div
                                class="absolute bottom-20 right-10 w-16 h-16 bg-white rounded-full animate-pulse delay-1000">
                            </div>
                            <div
                                class="absolute top-1/2 left-1/4 w-12 h-12 bg-white rounded-full animate-pulse delay-500">
                            </div>
                        </div>
                        <!-- Main Content -->
                        <div class="text-center text-white z-10">
                            <!-- Educational SVG Animation -->
                            <div class="mb-4 sm:mb-6 lg:mb-8 flex justify-center">
                                <SvgAnimation :size="150" class="sm:w-[200px] sm:h-[200px]" />
                            </div>
                            <h1 class="text-2xl sm:text-3xl font-bold mb-2 sm:mb-4">Join RPHMate</h1>
                            <p class="text-base sm:text-lg opacity-90 mb-1 sm:mb-2">Start Your Teaching Journey</p>
                            <p class="text-xs sm:text-sm opacity-75">Create your account and unlock comprehensive
                                educational
                                management tools
                            </p>
                        </div>
                    </div>
                    <!-- Right Panel - Signup Form -->
                    <div
                        class="lg:w-1/2 p-4 sm:p-6 lg:p-8 xl:p-12 flex items-center justify-center bg-white dark:bg-gray-900">
                        <ClientOnly>
                            <div class="w-full max-w-sm space-y-6 sm:space-y-8">
                                <div class="text-center">
                                    <h2 class="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white mb-2">Create
                                        Account
                                    </h2>
                                    <p class="text-xs sm:text-sm text-gray-600 dark:text-gray-400">Sign up to get
                                        started
                                        with
                                        RPHMate</p>
                                </div>
                                <form @submit.prevent="handleSignup" class="space-y-6">
                                    <div class="space-y-4">
                                        <div>
                                            <Input id="fullName" type="text" v-model="fullName" placeholder="Nama Penuh"
                                                required autocomplete="name" class="w-full" />
                                        </div>
                                        <div>
                                            <Input id="email" type="email" v-model="email"
                                                placeholder="Enter your email" required autocomplete="email"
                                                class="w-full" />
                                        </div>
                                        <div>
                                            <Input id="password" type="password" v-model="password"
                                                placeholder="Create a password" required autocomplete="password"
                                                class="w-full" />
                                        </div>
                                        <div>
                                            <Input id="confirmPassword" type="password" v-model="confirmPassword"
                                                placeholder="Confirm your password" required autocomplete="password"
                                                class="w-full" />
                                        </div>
                                    </div>

                                    <div class="flex items-start">
                                        <Checkbox id="terms" v-model="agreeToTerms" required />
                                        <label for="terms" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                                            I agree to the
                                            <a href="#"
                                                class="text-primary hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300">
                                                Terms of Service
                                            </a>
                                            and
                                            <a href="#"
                                                class="text-primary hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300">
                                                Privacy Policy
                                            </a>
                                        </label>
                                    </div> <Button type="submit" variant="primary" size="lg" class="w-full"
                                        :disabled="loading">
                                        {{ loading ? 'Menghantar...' : 'Hantar' }}
                                    </Button>

                                    <p v-if="errorMsg"
                                        class="text-red-500 text-sm text-center bg-red-50 dark:bg-red-900/20 p-3 rounded-md">
                                        {{ errorMsg }}
                                    </p>

                                    <p v-if="successMsg"
                                        class="text-green-500 text-sm text-center bg-green-50 dark:bg-green-900/20 p-3 rounded-md">
                                        {{ successMsg }}
                                    </p>
                                </form>

                                <div class="space-y-4">
                                    <div class="relative">
                                        <div class="absolute inset-0 flex items-center">
                                            <div class="w-full border-t border-gray-300 dark:border-gray-600"></div>
                                        </div>
                                        <div class="relative flex justify-center text-sm">
                                            <span
                                                class="px-2 bg-white dark:bg-gray-900 text-gray-500 dark:text-gray-400">Or
                                                continue with</span>
                                        </div>
                                    </div>

                                    <Button @click.prevent="handleGoogleSignup" variant="outline" size="lg"
                                        class="w-full" :disabled="googleLoading">
                                        <div class="flex items-center justify-center space-x-2">
                                            <Icon name="logos:google-icon" class="w-5 h-5 flex-shrink-0" />
                                            <span>{{ googleLoading ? 'Connecting...' : 'Sign up with Google' }}</span>
                                        </div>
                                    </Button>
                                </div>

                                <div class="text-center">
                                    <p class="text-sm text-gray-600 dark:text-gray-400">
                                        Already have an account?
                                        <a href="/auth/login"
                                            class="text-primary hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300 font-medium">
                                            Sign in here
                                        </a>
                                    </p>
                                </div>
                            </div>
                            <template #fallback>
                                <SkeletonTeacherSignup />
                            </template>
                        </ClientOnly>
                    </div>
                </div>
            </Card>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { useSupabase } from '~/composables/useSupabase';
import Button from '~/components/ui/base/Button.vue';
import Input from '~/components/ui/base/Input.vue';
import Card from '~/components/ui/composite/Card.vue';
import Icon from '~/components/ui/base/Icon.vue';
import Checkbox from '~/components/ui/base/Checkbox.vue';
import SvgAnimation from '~/components/ui/base/SvgAnimation.vue';
import SkeletonTeacherSignup from '~/components/ui/skeleton/SkeletonTeacherSignup.vue';

const { client } = useSupabase();
const router = useRouter();

const fullName = ref('');
const email = ref('');
const password = ref('');
const confirmPassword = ref('');
const agreeToTerms = ref(false);
const errorMsg = ref<string | null>(null);
const successMsg = ref<string | null>(null);
const loading = ref(false);
const googleLoading = ref(false);

// Add hydration-ready state
const isHydrated = ref(false);

// Preserve form values during hydration
const formState = ref({
    fullName: '',
    email: '',
    password: '',
    confirmPassword: '',
    agreeToTerms: false
});

const validateForm = (): boolean => {
    errorMsg.value = null;

    if (!fullName.value.trim()) {
        errorMsg.value = 'Nama penuh diperlukan.';
        return false;
    }

    if (!email.value.trim()) {
        errorMsg.value = 'Email is required.';
        return false;
    }

    if (password.value.length < 6) {
        errorMsg.value = 'Password must be at least 6 characters long.';
        return false;
    }

    if (password.value !== confirmPassword.value) {
        errorMsg.value = 'Passwords do not match.';
        return false;
    }

    if (!agreeToTerms.value) {
        errorMsg.value = 'You must agree to the Terms of Service and Privacy Policy.';
        return false;
    }

    return true;
};

const handleSignup = async () => {
    if (!validateForm()) return;

    loading.value = true;
    errorMsg.value = null;
    successMsg.value = null;

    try {
        const { data, error } = await client.auth.signUp({
            email: email.value,
            password: password.value,
            options: {
                emailRedirectTo: undefined, // Disable email confirmation redirect
                data: {
                    full_name: fullName.value
                }
            }
        });

        if (error) throw error;

        // For development: If user is created but needs confirmation, we'll handle it
        if (data.user) {
            // Update the profile created by the trigger with full_name
            // The trigger creates a basic profile, so we need to UPDATE it, not INSERT
            // Note: email is stored in auth.users, not in profiles table
            const { error: profileError } = await client.from('profiles').update({
                full_name: fullName.value,
                updated_at: new Date().toISOString()
            }).eq('id', data.user.id);

            if (profileError) {
                throw profileError;
            }

            // Create school membership
            try {
                const { ensureSchoolMembership } = useSchoolMembership()
                await ensureSchoolMembership(data.user.id, 'teacher')
            } catch (membershipError) {
                // Continue anyway - profile exists
            }

            // Store email for potential login later
            localStorage.setItem('pendingSignupEmail', email.value);
            localStorage.setItem('pendingSignupPassword', password.value);
            router.push('/dashboard');
        } else {
            router.push('/dashboard');
        }
    } catch (err: any) {
        if (err.message?.includes('already registered')) {
            errorMsg.value = 'An account with this email already exists. Please sign in instead.';
        } else if (err.message?.includes('Invalid email')) {
            errorMsg.value = 'Please enter a valid email address.';
        } else if (err.message?.includes('Password')) {
            errorMsg.value = 'Password is too weak. Please choose a stronger password.';
        } else {
            errorMsg.value = err.message || 'An unexpected error occurred during registration.';
        }
    } finally {
        loading.value = false;
    }
};

const handleGoogleSignup = async () => {
    errorMsg.value = null;
    successMsg.value = null;
    googleLoading.value = true;

    try {
        const { error } = await client.auth.signInWithOAuth({
            provider: 'google',
            options: {
                redirectTo: `${window.location.origin}/auth/confirm`,
                queryParams: {
                    prompt: 'select_account',
                }
            },
        });

        if (error) {
            throw error;
        }
        // The user will be redirected to Google by Supabase, then back to /auth/confirm
    } catch (err: any) {
        errorMsg.value = err.message || 'An unexpected error occurred with Google Sign-Up.';
    } finally {
        googleLoading.value = false;
    }
};

// Update form state whenever values change (only after hydration)
watch([fullName, email, password, confirmPassword, agreeToTerms], ([newFullName, newEmail, newPassword, newConfirmPassword, newAgreeToTerms]) => {
    if (isHydrated.value) {
        formState.value = {
            fullName: newFullName,
            email: newEmail,
            password: newPassword,
            confirmPassword: newConfirmPassword,
            agreeToTerms: newAgreeToTerms
        };

        // Store in sessionStorage to persist across any potential re-renders
        if (process.client) {
            sessionStorage.setItem('signupFormState', JSON.stringify(formState.value));
        }
    }
});

onMounted(async () => {
    // Restore any preserved values from sessionStorage immediately
    if (process.client) {
        try {
            const saved = sessionStorage.getItem('signupFormState');
            if (saved) {
                const savedState = JSON.parse(saved);
                fullName.value = savedState.fullName || '';
                email.value = savedState.email || '';
                password.value = savedState.password || '';
                confirmPassword.value = savedState.confirmPassword || '';
                agreeToTerms.value = savedState.agreeToTerms || false;
            }
        } catch (e) {
            // Ignore parsing errors
        }
    }

    // Mark as hydrated for state management
    isHydrated.value = true;

    // Clear saved state after successful restoration
    if (process.client) {
        sessionStorage.removeItem('signupFormState');
    }
});

definePageMeta({
    layout: 'blank',
});
</script>

<style scoped>
/* Custom gradient background animation */
@keyframes gradientShift {

    0%,
    100% {
        background-position: 0% 50%;
    }

    50% {
        background-position: 100% 50%;
    }
}

.bg-gradient-animated {
    background: linear-gradient(-45deg, #2563EB, #3B82F6, #1D4ED8, #1E40AF);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
}
</style>
