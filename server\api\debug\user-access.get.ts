// Debug endpoint to check user access and memberships
import { createClient } from '@supabase/supabase-js'

export default defineEventHandler(async (event) => {
  try {
    // Get authorization header
    const authHeader = getHeader(event, 'authorization')
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Missing or invalid authorization header'
      })
    }

    const token = authHeader.replace('Bearer ', '')

    // Initialize Supabase client
    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_ANON_KEY!,
      {
        global: {
          headers: {
            Authorization: authHeader
          }
        }
      }
    )

    // Verify the token and get user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)

    if (authError || !user) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Invalid or expired token'
      })
    }

    // Get user profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single()

    // Get user's school memberships
    const { data: memberships, error: membershipError } = await supabase
      .from('school_memberships')
      .select(`
        *,
        school:schools(*)
      `)
      .eq('user_id', user.id)

    // Get schools where user is admin
    const { data: adminSchools, error: adminError } = await supabase
      .from('schools')
      .select('*')
      .eq('admin_user_id', user.id)

    return {
      success: true,
      user: {
        id: user.id,
        email: user.email,
        user_metadata: user.user_metadata,
        identities: user.identities
      },
      profile: profile || null,
      profileError: profileError?.message || null,
      memberships: memberships || [],
      membershipError: membershipError?.message || null,
      adminSchools: adminSchools || [],
      adminError: adminError?.message || null,
      timestamp: new Date().toISOString()
    }

  } catch (error: any) {
    console.error('Debug user access error:', error)
    throw createError({
      statusCode: 500,
      statusMessage: error.message || 'Internal server error'
    })
  }
})
