// OAuth Configuration Checker
// Run this to check your Supabase OAuth setup

console.log('=== Supabase OAuth Configuration Check ===\n');

// Step 1: Check current environment
console.log('1. Environment Check:');
console.log('   Current URL:', window.location.href);
console.log('   Current Host:', window.location.host);
console.log('   Current Protocol:', window.location.protocol);

// Step 2: Check localStorage
console.log('\n2. LocalStorage Check:');
console.log('   oauth_school_code:', localStorage.getItem('oauth_school_code'));

// Step 3: Check URL parameters
console.log('\n3. URL Parameters:');
const urlParams = new URLSearchParams(window.location.search);
const hashParams = new URLSearchParams(window.location.hash.substring(1));

console.log('   Search params:');
for (const [key, value] of urlParams) {
    console.log(`     ${key}: ${value}`);
}

console.log('   Hash params:');
for (const [key, value] of hashParams) {
    console.log(`     ${key}: ${value}`);
}

// Step 4: Supabase configuration
console.log('\n4. Required Supabase Configuration:');
console.log('   In your Supabase Dashboard > Project Settings > Authentication > URL Configuration');
console.log('   Add these redirect URLs:');
console.log('   ✓ http://localhost:3000/auth/callback');
console.log('   ✓ https://localhost:3000/auth/callback (if using HTTPS)');

console.log('\n5. Current Issues Based on Console Log:');
console.log('   ❌ 400 Bad Request on token exchange');
console.log('   ❌ School code not being stored (null)');
console.log('   ❌ OAuth session not established');

console.log('\n6. Next Steps:');
console.log('   1. Add redirect URL to Supabase project settings');
console.log('   2. Try OAuth flow from subdomain (e.g., test.localhost:3000/auth/login)');
console.log('   3. Check that school code gets stored in localStorage');
console.log('   4. Verify OAuth callback completes successfully');

console.log('\n=== End Check ===');
