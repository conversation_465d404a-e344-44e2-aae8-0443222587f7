browser.mjs?v=40128521:48 ssr [dotenv@17.2.0] injecting env (0) from .env.local (tip: 🔐 prevent building .env in docker: https://dotenvx.com/prebuild) 
  at <anonymous> (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at Object.log (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at _log (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)
  at Object.configDotenv (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)
  at Object.config (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)

browser.mjs?v=40128521:48 ssr [dotenv@17.2.0] injecting env (0) from .env (tip: 🔐 prevent committing .env to code: https://dotenvx.com/precommit) 
  at <anonymous> (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at Object.log (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at _log (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)
  at Object.configDotenv (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)
  at Object.config (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)

browser.mjs?v=40128521:48 ssr 🏫 [SSR] Detected school subdomain: xba1224 
  at <anonymous> (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at Object.log (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at D:\XAMPP\htdocs\erphv9\plugins\subdomain-detection.server.ts
  at D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\app\nuxt.js
  at fn (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\app\nuxt.js)

subdomain-detection.client.ts:56 🏫 Detected school subdomain: xba1224
a-subdomain.global.ts:288 Fetch finished loading: POST "http://xba1224.localhost:3000/api/schools/exists".
(anonymous) @ index.mjs?v=40128521:21
$fetchRaw2 @ ofetch.03887fc3.mjs?v=40128521:258
$fetch2 @ ofetch.03887fc3.mjs?v=40128521:316
validateSchoolExistsWithCache @ a-subdomain.global.ts:288
(anonymous) @ a-subdomain.global.ts:54
executeAsync @ index.mjs?v=40128521:110
(anonymous) @ a-subdomain.global.ts:54
(anonymous) @ router.js?v=40128521:169
fn @ nuxt.js?v=40128521:216
runWithContext @ runtime-core.esm-bundler.js?v=40128521:4016
callWithNuxt @ nuxt.js?v=40128521:222
(anonymous) @ nuxt.js?v=40128521:38
run @ reactivity.esm-bundler.js?v=40128521:67
runWithContext @ nuxt.js?v=40128521:38
(anonymous) @ router.js?v=40128521:169
await in (anonymous)
(anonymous) @ vue-router.mjs?v=40128521:1385
runWithContext @ vue-router.mjs?v=40128521:1360
(anonymous) @ vue-router.mjs?v=40128521:1385
(anonymous) @ vue-router.mjs?v=40128521:1363
runWithContext @ runtime-core.esm-bundler.js?v=40128521:4016
runWithContext @ vue-router.mjs?v=40128521:2426
(anonymous) @ vue-router.mjs?v=40128521:2698
Promise.then
(anonymous) @ vue-router.mjs?v=40128521:2698
runGuardQueue @ vue-router.mjs?v=40128521:2698
(anonymous) @ vue-router.mjs?v=40128521:2445
Promise.then
navigate @ vue-router.mjs?v=40128521:2439
pushWithRedirect @ vue-router.mjs?v=40128521:2372
push @ vue-router.mjs?v=40128521:2308
replace @ vue-router.mjs?v=40128521:2311
(anonymous) @ router.js?v=40128521:223
_function @ index.mjs?v=40128521:133
(anonymous) @ index.mjs?v=40128521:48
(anonymous) @ index.mjs?v=40128521:48
app:created
serialTaskCaller @ index.mjs?v=40128521:46
callHookWith @ index.mjs?v=40128521:198
callHook @ index.mjs?v=40128521:187
initApp @ entry.js:65
await in initApp
(anonymous) @ entry.js:75
runtime-core.esm-bundler.js?v=40128521:7060 <Suspense> is an experimental feature and its API will likely change.
devtools.client.js?v=40128521:52 ✨ Nuxt DevTools  Press Shift + Alt + D to open DevTools 
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
daftar.vue:70  POST https://nhgyywlfopodxomxbegx.supabase.co/auth/v1/signup 422 (Unprocessable Content)
fetchWithRetry @ fetch-retry.js?v=40128521:5
(anonymous) @ helpers.js?v=40128521:87
_handleRequest @ fetch.js?v=40128521:101
_request @ fetch.js?v=40128521:91
signUp @ GoTrueClient.js?v=40128521:280
await in signUp
handleSignup @ daftar.vue:70
cache.<computed>.cache.<computed> @ runtime-dom.esm-bundler.js?v=40128521:1683
callWithErrorHandling @ runtime-core.esm-bundler.js?v=40128521:197
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js?v=40128521:204
invoker @ runtime-dom.esm-bundler.js?v=40128521:720
daftar.vue:70 Fetch failed loading: POST "https://nhgyywlfopodxomxbegx.supabase.co/auth/v1/signup".
fetchWithRetry @ fetch-retry.js?v=40128521:5
(anonymous) @ helpers.js?v=40128521:87
_handleRequest @ fetch.js?v=40128521:101
_request @ fetch.js?v=40128521:91
signUp @ GoTrueClient.js?v=40128521:280
await in signUp
handleSignup @ daftar.vue:70
cache.<computed>.cache.<computed> @ runtime-dom.esm-bundler.js?v=40128521:1683
callWithErrorHandling @ runtime-core.esm-bundler.js?v=40128521:197
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js?v=40128521:204
invoker @ runtime-dom.esm-bundler.js?v=40128521:720
daftar.vue:70 Fetch finished loading: POST "https://nhgyywlfopodxomxbegx.supabase.co/auth/v1/signup".
fetchWithRetry @ fetch-retry.js?v=40128521:5
(anonymous) @ helpers.js?v=40128521:87
_handleRequest @ fetch.js?v=40128521:101
_request @ fetch.js?v=40128521:91
signUp @ GoTrueClient.js?v=40128521:280
await in signUp
handleSignup @ daftar.vue:70
cache.<computed>.cache.<computed> @ runtime-dom.esm-bundler.js?v=40128521:1683
callWithErrorHandling @ runtime-core.esm-bundler.js?v=40128521:197
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js?v=40128521:204
invoker @ runtime-dom.esm-bundler.js?v=40128521:720
fetch-retry.js?v=40128521:5  POST https://nhgyywlfopodxomxbegx.supabase.co/rest/v1/profiles 400 (Bad Request)
fetchWithRetry @ fetch-retry.js?v=40128521:5
(anonymous) @ fetch.js?v=40128521:23
(anonymous) @ fetch.js?v=40128521:44
fulfilled @ fetch.js?v=40128521:4
Promise.then
step @ fetch.js?v=40128521:6
(anonymous) @ fetch.js?v=40128521:7
__awaiter @ fetch.js?v=40128521:3
(anonymous) @ fetch.js?v=40128521:34
then @ @nuxtjs_supabase___@supabase_postgrest-js.js?v=40128521:119
fetch-retry.js?v=40128521:5 Fetch failed loading: POST "https://nhgyywlfopodxomxbegx.supabase.co/rest/v1/profiles".
fetchWithRetry @ fetch-retry.js?v=40128521:5
(anonymous) @ fetch.js?v=40128521:23
(anonymous) @ fetch.js?v=40128521:44
fulfilled @ fetch.js?v=40128521:4
Promise.then
step @ fetch.js?v=40128521:6
(anonymous) @ fetch.js?v=40128521:7
__awaiter @ fetch.js?v=40128521:3
(anonymous) @ fetch.js?v=40128521:34
then @ @nuxtjs_supabase___@supabase_postgrest-js.js?v=40128521:119
useSchoolMembership.ts:168 [useSchoolMembership] Ensuring membership for user 7ab824ad-c1e1-476d-9c3e-4d774497ffb1 in school xba1224
useSchoolMembership.ts:94 [useSchoolMembership] Creating membership for user 7ab824ad-c1e1-476d-9c3e-4d774497ffb1 in school xba1224
useSchoolMembership.ts:108 [useSchoolMembership] Found school: SK Tiong Widu (d3fb479c-1fc4-4ac2-a9f1-246b7c7b800c)
fetch-retry.js?v=40128521:5  GET https://nhgyywlfopodxomxbegx.supabase.co/rest/v1/school_memberships?select=*&user_id=eq.7ab824ad-c1e1-476d-9c3e-4d774497ffb1&school_id=eq.d3fb479c-1fc4-4ac2-a9f1-246b7c7b800c&status=eq.active 406 (Not Acceptable)
fetchWithRetry @ fetch-retry.js?v=40128521:5
(anonymous) @ fetch.js?v=40128521:23
(anonymous) @ fetch.js?v=40128521:44
fulfilled @ fetch.js?v=40128521:4
Promise.then
step @ fetch.js?v=40128521:6
(anonymous) @ fetch.js?v=40128521:7
__awaiter @ fetch.js?v=40128521:3
(anonymous) @ fetch.js?v=40128521:34
then @ @nuxtjs_supabase___@supabase_postgrest-js.js?v=40128521:119
fetch-retry.js?v=40128521:5 Fetch failed loading: GET "https://nhgyywlfopodxomxbegx.supabase.co/rest/v1/school_memberships?select=*&user_id=eq.7ab824ad-c1e1-476d-9c3e-4d774497ffb1&school_id=eq.d3fb479c-1fc4-4ac2-a9f1-246b7c7b800c&status=eq.active".
fetchWithRetry @ fetch-retry.js?v=40128521:5
(anonymous) @ fetch.js?v=40128521:23
(anonymous) @ fetch.js?v=40128521:44
fulfilled @ fetch.js?v=40128521:4
Promise.then
step @ fetch.js?v=40128521:6
(anonymous) @ fetch.js?v=40128521:7
__awaiter @ fetch.js?v=40128521:3
(anonymous) @ fetch.js?v=40128521:34
then @ @nuxtjs_supabase___@supabase_postgrest-js.js?v=40128521:119
fetch-retry.js?v=40128521:5 Fetch finished loading: POST "https://nhgyywlfopodxomxbegx.supabase.co/rest/v1/school_memberships?select=*".
fetchWithRetry @ fetch-retry.js?v=40128521:5
(anonymous) @ fetch.js?v=40128521:23
(anonymous) @ fetch.js?v=40128521:44
fulfilled @ fetch.js?v=40128521:4
Promise.then
step @ fetch.js?v=40128521:6
(anonymous) @ fetch.js?v=40128521:7
__awaiter @ fetch.js?v=40128521:3
(anonymous) @ fetch.js?v=40128521:34
then @ @nuxtjs_supabase___@supabase_postgrest-js.js?v=40128521:119
useSchoolMembership.ts:138 [useSchoolMembership] School membership created successfully: {id: '72bad300-96ca-4ba6-9cb0-a37b540769c5', school_id: 'd3fb479c-1fc4-4ac2-a9f1-246b7c7b800c', user_id: '7ab824ad-c1e1-476d-9c3e-4d774497ffb1', role: 'teacher', status: 'active', …}
daftar.vue:92 [daftar.vue] School membership created successfully: {id: '72bad300-96ca-4ba6-9cb0-a37b540769c5', school_id: 'd3fb479c-1fc4-4ac2-a9f1-246b7c7b800c', user_id: '7ab824ad-c1e1-476d-9c3e-4d774497ffb1', role: 'teacher', status: 'active', …}
useSchoolContext.ts:155 ✅ School context loaded: SK Tiong Widu (xba1224)
