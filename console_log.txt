browser.mjs?v=40128521:48 ssr [dotenv@17.2.0] injecting env (0) from .env.local (tip: 🔐 prevent building .env in docker: https://dotenvx.com/prebuild) 
  at <anonymous> (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at Object.log (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at _log (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)
  at Object.configDotenv (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)
  at Object.config (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)

browser.mjs?v=40128521:48 ssr [dotenv@17.2.0] injecting env (0) from .env (tip: ⚙️  load multiple .env files with { path: ['.env.local', '.env'] }) 
  at <anonymous> (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at Object.log (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at _log (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)
  at Object.configDotenv (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)
  at Object.config (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)

browser.mjs?v=40128521:48 ssr 🏫 [SSR] Detected school subdomain: xba1224 
  at <anonymous> (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at Object.log (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at D:\XAMPP\htdocs\erphv9\plugins\subdomain-detection.server.ts
  at D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\app\nuxt.js
  at fn (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\app\nuxt.js)

subdomain-detection.client.ts:56 🏫 Detected school subdomain: xba1224
a-subdomain.global.ts:275 Fetch finished loading: POST "http://xba1224.localhost:3000/api/schools/exists".
(anonymous) @ index.mjs?v=40128521:21
$fetchRaw2 @ ofetch.03887fc3.mjs?v=40128521:258
$fetch2 @ ofetch.03887fc3.mjs?v=40128521:316
validateSchoolExistsWithCache @ a-subdomain.global.ts:275
(anonymous) @ a-subdomain.global.ts:54
executeAsync @ index.mjs?v=40128521:110
(anonymous) @ a-subdomain.global.ts:54
(anonymous) @ router.js?t=1753003406035&v=40128521:169
fn @ nuxt.js?v=40128521:216
runWithContext @ runtime-core.esm-bundler.js?v=40128521:4016
callWithNuxt @ nuxt.js?v=40128521:222
(anonymous) @ nuxt.js?v=40128521:38
run @ reactivity.esm-bundler.js?v=40128521:67
runWithContext @ nuxt.js?v=40128521:38
(anonymous) @ router.js?t=1753003406035&v=40128521:169
await in (anonymous)
(anonymous) @ vue-router.mjs?v=40128521:1385
runWithContext @ vue-router.mjs?v=40128521:1360
(anonymous) @ vue-router.mjs?v=40128521:1385
(anonymous) @ vue-router.mjs?v=40128521:1363
runWithContext @ runtime-core.esm-bundler.js?v=40128521:4016
runWithContext @ vue-router.mjs?v=40128521:2426
(anonymous) @ vue-router.mjs?v=40128521:2698
Promise.then
(anonymous) @ vue-router.mjs?v=40128521:2698
runGuardQueue @ vue-router.mjs?v=40128521:2698
(anonymous) @ vue-router.mjs?v=40128521:2445
Promise.then
navigate @ vue-router.mjs?v=40128521:2439
pushWithRedirect @ vue-router.mjs?v=40128521:2372
push @ vue-router.mjs?v=40128521:2308
replace @ vue-router.mjs?v=40128521:2311
(anonymous) @ router.js?t=1753003406035&v=40128521:223
_function @ index.mjs?v=40128521:133
(anonymous) @ index.mjs?v=40128521:48
(anonymous) @ index.mjs?v=40128521:48
app:created
serialTaskCaller @ index.mjs?v=40128521:46
callHookWith @ index.mjs?v=40128521:198
callHook @ index.mjs?v=40128521:187
initApp @ entry.js:65
await in initApp
(anonymous) @ entry.js:75
runtime-core.esm-bundler.js?v=40128521:7060 <Suspense> is an experimental feature and its API will likely change.
fetch-retry.js?v=40128521:5 Fetch finished loading: POST "https://nhgyywlfopodxomxbegx.supabase.co/auth/v1/token?grant_type=pkce".
fetchWithRetry @ fetch-retry.js?v=40128521:5
(anonymous) @ helpers.js?v=40128521:87
_handleRequest @ fetch.js?v=40128521:101
_request @ fetch.js?v=40128521:91
_exchangeCodeForSession @ GoTrueClient.js?v=40128521:417
await in _exchangeCodeForSession
_getSessionFromURL @ GoTrueClient.js?v=40128521:1102
_initialize @ GoTrueClient.js?v=40128521:177
await in _initialize
(anonymous) @ GoTrueClient.js?v=40128521:148
(anonymous) @ GoTrueClient.js?v=40128521:751
(anonymous) @ locks.js?v=40128521:89
devtools.client.js?v=40128521:52 ✨ Nuxt DevTools  Press Shift + Alt + D to open DevTools 
confirm.vue:368 🚀 [confirm.vue] Component mounted, starting OAuth confirmation flow
confirm.vue:374 🌐 [confirm.vue] Current host: xba1224.localhost:3000
confirm.vue:375 🌐 [confirm.vue] Is main domain: false
confirm.vue:403 🔍 [confirm.vue] Checking session...
confirm.vue:406 ✅ [confirm.vue] Session found, user ID: 4962e5b7-95f4-43c5-9864-03ddb9d453a3
confirm.vue:407 👤 [confirm.vue] User metadata: {avatar_url: 'https://lh3.googleusercontent.com/a/ACg8ocIVRhzhDOHZ-v1gFP69_OrQ7xECIxpkTGv_eHjDUXlIn5CVQQ=s96-c', custom_claims: {…}, email: '<EMAIL>', email_verified: true, full_name: 'COURTNEY V. SUNGGIP KPM-Guru', …}
confirm.vue:408 🔗 [confirm.vue] User identities: [{…}]
confirm.vue:134 🔄 [confirm.vue] handleSuccessfulSignIn called
confirm.vue:159 ✅ [confirm.vue] Starting profile verification for user: 4962e5b7-95f4-43c5-9864-03ddb9d453a3
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
confirm.vue:191 ❌ [confirm.vue] Profile not found, attempting to create
confirm.vue:195 🔧 [confirm.vue] First attempt - creating user profile
confirm.vue:204 👤 [confirm.vue] User data for profile creation: {id: '4962e5b7-95f4-43c5-9864-03ddb9d453a3', email: '<EMAIL>', user_metadata: {…}, identities: Array(1), app_metadata: {…}}
confirm.vue:226 📝 [confirm.vue] Extracted full name: COURTNEY V. SUNGGIP KPM-Guru
confirm.vue:236 💾 [confirm.vue] Profile data to update: {id: '4962e5b7-95f4-43c5-9864-03ddb9d453a3', full_name: 'COURTNEY V. SUNGGIP KPM-Guru', avatar_url: 'https://lh3.googleusercontent.com/a/ACg8ocIVRhzhDOHZ-v1gFP69_OrQ7xECIxpkTGv_eHjDUXlIn5CVQQ=s96-c', created_at: '2025-07-20T09:25:18.809Z', updated_at: '2025-07-20T09:25:18.809Z'}
fetch-retry.js?v=40128521:5  PATCH https://nhgyywlfopodxomxbegx.supabase.co/rest/v1/profiles?id=eq.4962e5b7-95f4-43c5-9864-03ddb9d453a3&select=* 406 (Not Acceptable)
fetchWithRetry @ fetch-retry.js?v=40128521:5
(anonymous) @ fetch.js?v=40128521:23
(anonymous) @ fetch.js?v=40128521:44
fulfilled @ fetch.js?v=40128521:4
Promise.then
step @ fetch.js?v=40128521:6
(anonymous) @ fetch.js?v=40128521:7
__awaiter @ fetch.js?v=40128521:3
(anonymous) @ fetch.js?v=40128521:34
then @ @nuxtjs_supabase___@supabase_postgrest-js.js?v=40128521:119
fetch-retry.js?v=40128521:5 Fetch failed loading: PATCH "https://nhgyywlfopodxomxbegx.supabase.co/rest/v1/profiles?id=eq.4962e5b7-95f4-43c5-9864-03ddb9d453a3&select=*".
fetchWithRetry @ fetch-retry.js?v=40128521:5
(anonymous) @ fetch.js?v=40128521:23
(anonymous) @ fetch.js?v=40128521:44
fulfilled @ fetch.js?v=40128521:4
Promise.then
step @ fetch.js?v=40128521:6
(anonymous) @ fetch.js?v=40128521:7
__awaiter @ fetch.js?v=40128521:3
(anonymous) @ fetch.js?v=40128521:34
then @ @nuxtjs_supabase___@supabase_postgrest-js.js?v=40128521:119
confirm.vue:251 ❌ [confirm.vue] Profile update error: {code: 'PGRST116', details: 'The result contains 0 rows', hint: null, message: 'JSON object requested, multiple (or no) rows returned'}
attemptProfileVerification @ confirm.vue:251
await in attemptProfileVerification
handleSuccessfulSignIn @ confirm.vue:363
(anonymous) @ confirm.vue:409
Promise.then
(anonymous) @ confirm.vue:404
(anonymous) @ runtime-core.esm-bundler.js?v=40128521:2844
callWithErrorHandling @ runtime-core.esm-bundler.js?v=40128521:197
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js?v=40128521:204
hook.__weh.hook.__weh @ runtime-core.esm-bundler.js?v=40128521:2824
flushPostFlushCbs @ runtime-core.esm-bundler.js?v=40128521:382
flushJobs @ runtime-core.esm-bundler.js?v=40128521:424
Promise.then
queueFlush @ runtime-core.esm-bundler.js?v=40128521:319
queuePostFlushCb @ runtime-core.esm-bundler.js?v=40128521:333
resolve @ runtime-core.esm-bundler.js?v=40128521:7164
resolve @ runtime-core.esm-bundler.js?v=40128521:7171
(anonymous) @ runtime-core.esm-bundler.js?v=40128521:7270
Promise.then
registerDep @ runtime-core.esm-bundler.js?v=40128521:7234
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5250
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=40128521:7311
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=40128521:7311
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrate @ runtime-core.esm-bundler.js?v=40128521:1673
mount @ runtime-core.esm-bundler.js?v=40128521:3959
app.mount @ runtime-dom.esm-bundler.js?v=40128521:1763
initApp @ entry.js:67
await in initApp
(anonymous) @ entry.js:75
confirm.vue:191 ❌ [confirm.vue] Profile not found, attempting to create
confirm.vue:285 🔄 [confirm.vue] Profile creation attempt 2 (not first attempt)
confirm.vue:191 ❌ [confirm.vue] Profile not found, attempting to create
confirm.vue:285 🔄 [confirm.vue] Profile creation attempt 3 (not first attempt)
confirm.vue:191 ❌ [confirm.vue] Profile not found, attempting to create
confirm.vue:285 🔄 [confirm.vue] Profile creation attempt 4 (not first attempt)
confirm.vue:191 ❌ [confirm.vue] Profile not found, attempting to create
confirm.vue:285 🔄 [confirm.vue] Profile creation attempt 5 (not first attempt)
useSchoolContext.ts:155 ✅ School context loaded: SK Tiong Widu (xba1224)
