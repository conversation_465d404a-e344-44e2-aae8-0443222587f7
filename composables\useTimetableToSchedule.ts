import { ref, computed } from 'vue'
import type { TimetableEntry, DayOfWeek } from '~/types/timetable'
import type { TeacherScheduleWithDetails } from '~/types/teacherSchedule'
import { formatTimeRange } from '~/utils/timeHelpers'
import { useTimetable } from '~/composables/useTimetable'

// Auto-generated teaching schedule from timetable entries
export interface AutoGeneratedSchedule {
  class_id: string
  subject_id: string
  class_name: string
  subject_name: string
  days_scheduled: DayOfWeek[]
  total_periods: number
  time_periods: {
    day: DayOfWeek
    time_slot_start: string
    time_slot_end: string
    time_label: string
  }[]
  created_at: string
  updated_at: string
}

export const useTimetableToSchedule = () => {
  // Use the real timetable composable to get actual data
  const { timetableEntries, loading, fetchTimetableEntries } = useTimetable()
  const error = ref<string | null>(null)
  // Define proper day order for sorting
  const dayOrder: Record<DayOfWeek, number> = {
    'AHAD': 0,
    'ISNIN': 1,
    'SELASA': 2,
    'RABU': 3,
    'KHAMIS': 4,
    'JUMAAT': 5
  }

  // Helper function to sort days properly
  const sortDays = (days: DayOfWeek[]): DayOfWeek[] => {
    return days.sort((a, b) => dayOrder[a] - dayOrder[b])
  }
  // Auto-generate teaching schedules from timetable entries
  const autoGeneratedSchedules = computed((): AutoGeneratedSchedule[] => {
    if (timetableEntries.value.length === 0) return []

    // Filter to only include CLASS activities and group by class-subject combination
    const classEntries = timetableEntries.value.filter(entry => 
      entry.activity_type === 'CLASS' && 
      entry.class_id && 
      entry.subject_id
    )
    
    if (classEntries.length === 0) return []

    const groupedEntries = new Map<string, TimetableEntry[]>()
    
    classEntries.forEach(entry => {
      const key = `${entry.class_id}-${entry.subject_id}`
      if (!groupedEntries.has(key)) {
        groupedEntries.set(key, [])
      }
      groupedEntries.get(key)!.push(entry)
    })// Convert groups to auto-generated schedules
    return Array.from(groupedEntries.entries()).map(([key, entries]) => {
      const firstEntry = entries[0]
      const uniqueDays = [...new Set(entries.map(e => e.day))]
      const sortedDays = sortDays(uniqueDays)
        return {
        class_id: firstEntry.class_id!, // Safe to assert since we filtered for non-null
        subject_id: firstEntry.subject_id!, // Safe to assert since we filtered for non-null
        class_name: firstEntry.class_name!, // Safe to assert since we filtered for non-null
        subject_name: firstEntry.subject_name!, // Safe to assert since we filtered for non-null
        days_scheduled: sortedDays,
        total_periods: entries.length,time_periods: entries.map(entry => ({
          day: entry.day,
          time_slot_start: entry.time_slot_start,
          time_slot_end: entry.time_slot_end,
          time_label: getTimeSlotLabel(entry.time_slot_start, entry.time_slot_end)
        })),
        created_at: Math.min(...entries.map(e => new Date(e.created_at).getTime())).toString(),
        updated_at: Math.max(...entries.map(e => new Date(e.updated_at).getTime())).toString()
      }
    })
  })

  // Statistics computed from auto-generated schedules
  const totalPeriodsCount = computed(() => {
    return autoGeneratedSchedules.value.reduce((total, schedule) => {
      return total + schedule.total_periods
    }, 0)
  })

  const uniqueSubjectsCount = computed(() => {
    const subjectIds = new Set(autoGeneratedSchedules.value.map(s => s.subject_id))
    return subjectIds.size
  })

  const uniqueClassesCount = computed(() => {
    const classIds = new Set(autoGeneratedSchedules.value.map(s => s.class_id))
    return classIds.size
  })  // Helper function to get time slot label
  const getTimeSlotLabel = (timeSlotStart: string, timeSlotEnd: string): string => {
    return formatTimeRange(timeSlotStart, timeSlotEnd)
  }
  // Load timetable entries from the real timetable composable
  const loadTimetableEntries = async () => {
    try {
      await fetchTimetableEntries()
    } catch (err) {
      error.value = 'Failed to load timetable entries'
      console.error('Error loading timetable entries:', err)
    }
  }
  // Convert auto-generated schedules to the format expected by existing components
  const schedulesWithDetails = computed((): TeacherScheduleWithDetails[] => {
    return autoGeneratedSchedules.value.map(schedule => ({
      id: `auto_${schedule.class_id}_${schedule.subject_id}`,
      user_id: 'current_user', // This should be replaced with the actual user ID when available
      lesson_plan_id: null,
      school_id: null, // Auto-generated schedules don't have a specific school context
      created_at: schedule.created_at,
      updated_at: schedule.updated_at,
      class_name: schedule.class_name,
      subject_name: schedule.subject_name,
      total_periods: schedule.total_periods,
      days_scheduled: schedule.days_scheduled,
      schedule_details: {
        class_subjects: [{
          class_id: schedule.class_id,
          class_name: schedule.class_name,
          subject_id: schedule.subject_id,
          subject_name: schedule.subject_name,
          days_scheduled: schedule.days_scheduled,
          total_periods: schedule.total_periods,
          periods: schedule.time_periods.map(p => ({
            day: p.day,
            time_slot_start: p.time_slot_start,
            time_slot_end: p.time_slot_end,
            class_id: schedule.class_id,
            subject_id: schedule.subject_id,
            class_name: schedule.class_name,
            subject_name: schedule.subject_name,
            class_subject_id: `${schedule.class_id}_${schedule.subject_id}`
          }))
        }]
      }
    }));
  });  // Day options for display (in proper Malaysian week order)
  const dayOptions = ref([
    { value: 'AHAD', label: 'Ahad' },
    { value: 'ISNIN', label: 'Isnin' },
    { value: 'SELASA', label: 'Selasa' },
    { value: 'RABU', label: 'Rabu' },
    { value: 'KHAMIS', label: 'Khamis' },
    { value: 'JUMAAT', label: 'Jumaat' }
  ])

  return {
    // State
    timetableEntries,
    loading,
    error,
    
    // Auto-generated data
    autoGeneratedSchedules,
    schedulesWithDetails,
    
    // Statistics
    totalPeriodsCount,
    uniqueSubjectsCount,
    uniqueClassesCount,
    
    // Actions
    loadTimetableEntries,
    
    // Helpers
    dayOptions
  }
}
