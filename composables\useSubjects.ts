import { ref } from "vue";
import { useSupabaseClient, useSupabaseUser } from "#imports";
import type { Database } from "~/types/supabase";
import type { PostgrestError } from "@supabase/supabase-js";

export interface Subject {
  id: string; // UUID
  name: string; // Full subject name
  code: string; // Short code, unique
  user_id: string | null; // Associated user if custom, null if global/predefined
  is_custom: boolean;
  category: string | null; // teras, tambahan, pilihan, elektif_sains, elektif_sastera
  level_type: string | null; // tahun, tingkatan, both
  sub_level: string | null; // all, lower (1-3), upper (4-5)
  sort_order: number | null; // Display order within category
  is_active: boolean | null; // Whether subject is currently active/available
  created_at: string;
  updated_at: string;
  school_id: string | null; // School ID for school-specific subjects
}

// --- Singleton State ---
// By defining the state outside the composable function, we ensure that
// every component calling useSubjects() shares the same reactive state.
const subjects = ref<Subject[]>([]);
const loading = ref(false);
const error = ref<PostgrestError | null>(null);
let initialFetchCompleted = false; // Flag to track if the base set of subjects has been fetched

export function useSubjects() {
  const client = useSupabaseClient<Database>();
  const user = useSupabaseUser();

  async function fetchSubjects(additionalIds: string[] = []) {
    // Prevent re-fetching if already loading
    if (loading.value) return;

    const existingIds = new Set(subjects.value.map((s) => s.id));
    const filters = [];

    // On the very first fetch for this session, get the base set of subjects.
    if (!initialFetchCompleted) {
      // Always fetch global subjects (user_id is null)
      filters.push(`user_id.is.null`);

      // If user is authenticated, also fetch their custom subjects
      if (user.value) {
        filters.push(`user_id.eq.${user.value.id}`);
      }
    }

    // Filter for additional IDs that are not already in our state.
    const uniqueNewIds = [...new Set(additionalIds)].filter(
      (id) => id && id !== "null" && !existingIds.has(id)
    );

    if (uniqueNewIds.length > 0) {
      filters.push(`id.in.(${uniqueNewIds.join(",")})`);
    }

    // If there are no filters to apply (e.g., initial fetch is done and no new IDs were provided),
    // we don't need to make a network request.
    if (filters.length === 0) {
      return;
    }

    loading.value = true;
    error.value = null;
    try {
      // Fetch subjects based on the constructed filters
      const { data: rawData, error: fetchError } = await client
        .from("subjects")
        .select("*")
        .or(filters.join(","))
        .order("name", { ascending: true });

      if (fetchError) throw fetchError;

      if (rawData) {
        const fetchedSubjects = rawData
          .map((item) => {
            if (
              !item.id ||
              typeof item.id !== "string" ||
              item.id.length < 10
            ) {
              console.warn(
                `Invalid or missing ID for subject: '${
                  item.name || "Unknown Name"
                }', raw ID: '${item.id}'. Skipping this subject.`
              );
              return null;
            }
            return {
              ...item,
              id: item.id, // Use ID as string
            } as Subject;
          })
          .filter((subject): subject is Subject => subject !== null);

        // Merge with existing subjects to avoid overwriting and ensure reactivity.
        const subjectsMap = new Map(subjects.value.map((s) => [s.id, s]));
        fetchedSubjects.forEach((s) => subjectsMap.set(s.id, s));

        const mergedAndSortedSubjects = Array.from(subjectsMap.values()).sort(
          (a, b) => a.name.localeCompare(b.name)
        );

        subjects.value = mergedAndSortedSubjects;
      }

      // If this was the initial fetch, mark it as completed.
      if (!initialFetchCompleted) {
        initialFetchCompleted = true;
      }
    } catch (err) {
      error.value = err as PostgrestError;
      console.error("Error fetching subjects:", err);
    } finally {
      loading.value = false;
    }
  }

  /**
   * Fetches a single subject by its ID if it's not already in the local state.
   * @param subjectId The ID of the subject to fetch.
   */
  async function fetchSubjectById(subjectId: string) {
    if (!subjectId) return;

    const existingSubject = subjects.value.find((s) => s.id === subjectId);
    if (existingSubject) {
      return existingSubject; // Return from local state if available
    }

    // If not found locally, fetch it using the main fetcher
    await fetchSubjects([subjectId]);
    return subjects.value.find((s) => s.id === subjectId);
  }

  /**
   * Adds a new custom subject for the current user.
   * @param subjectData The data for the new subject.
   * @returns The newly created subject.
   */
  async function addSubject(
    subjectData: Omit<
      Subject,
      "id" | "user_id" | "is_custom" | "created_at" | "updated_at"
    >
  ): Promise<Subject | null> {
    if (!user.value) {
      error.value = {
        message: "User not authenticated.",
        code: "401",
        details: "",
        hint: "",
        name: "AuthError",
      };
      return null;
    }

    loading.value = true;
    error.value = null;
    try {
      const { data, error: insertError } = await client
        .from("subjects")
        .insert({
          ...subjectData,
          user_id: user.value.id,
          is_custom: true,
        })
        .select()
        .single();

      if (insertError) {
        console.error("Supabase insert error:", insertError);
        throw new Error(`Failed to create subject: ${insertError.message}`);
      }

      if (!data?.id) {
        throw new Error("Subject created but no ID returned from database");
      }

      // Ensure proper typing and ID handling
      const newSubject: Subject = {
        ...data,
        id: data.id, // Keep as UUID string from database
        user_id: data.user_id,
        is_custom: true,
        name: data.name,
        code: data.code,
        created_at: data.created_at,
        updated_at: data.updated_at
      };

      // Add to local state and keep sorted
      subjects.value = [...subjects.value, newSubject].sort((a, b) =>
        a.name.localeCompare(b.name)
      );
      
      console.log("Successfully created subject:", newSubject);
      
      // Update user's profile with the new subject
      await updateUserProfileWithSubject(newSubject.id);
      
      return newSubject;
    } catch (err) {
      error.value = err as PostgrestError;
      console.error("Error adding subject:", err);
      return null;
    } finally {
      loading.value = false;
    }
  }

  /**
   * Updates the user's profile to include the new subject in class_subjects
   * @param subjectId The ID of the newly created subject
   */
  async function updateUserProfileWithSubject(subjectId: string) {
    if (!user.value) return;
    
    try {
      // Fetch the current user's profile
      const { data: profileData, error: profileError } = await client
        .from('profiles')
        .select('class_subjects')
        .eq('id', user.value.id)
        .single();
        
      if (profileError) throw profileError;
      
      // Handle different possible types for class_subjects
      let currentClassSubjects: any[] = [];
      if (Array.isArray(profileData.class_subjects)) {
        currentClassSubjects = profileData.class_subjects;
      } else if (profileData.class_subjects && typeof profileData.class_subjects === 'object') {
        // Convert object to array if it's a single object
        currentClassSubjects = [profileData.class_subjects];
      }
      
      // Add the new subject to class_subjects
      const updatedClassSubjects = [
        ...currentClassSubjects,
        {
          subject_id: subjectId,
          // Placeholder values - these will be updated when user completes the form
          class_id: '',
          className: '',
          subject_abbreviation: '',
          studentCount: null
        }
      ];
      
      // Update the profile
      const { error: updateError } = await client
        .from('profiles')
        .update({ class_subjects: updatedClassSubjects })
        .eq('id', user.value.id);
        
      if (updateError) throw updateError;
      
      console.log('Successfully updated user profile with new subject');
    } catch (err) {
      console.error('Error updating user profile:', err);
    }
  }

  return {
    subjects,
    loading,
    error,
    fetchSubjects,
    fetchSubjectById,
    addSubject,
  };
}
