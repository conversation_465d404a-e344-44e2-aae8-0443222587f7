<template>
    <div
        class="flex h-screen bg-light-background dark:bg-dark-background text-light-foreground dark:text-dark-foreground">
        <!-- Sidebar -->
        <UiLayoutSidebar :is-mobile-open="isMobileSidebarOpen" :school-context="schoolContext"
            @close="closeMobileSidebar" @logout="handleLogout" />

        <!-- Main Content Area -->
        <div class="flex-1 flex flex-col overflow-hidden lg:ml-64">
            <!-- Header -->
            <UiLayoutHeader :school-context="schoolContext" @toggle-mobile-sidebar="toggleMobileSidebar" />

            <!-- School Context Banner (if user has multiple schools) -->
            <div v-if="userSchools.length > 1"
                class="bg-blue-50 dark:bg-blue-900/20 border-b border-blue-200 dark:border-blue-800 px-4 py-2">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-2">
                        <Icon name="heroicons:building-office" class="h-4 w-4 text-blue-600 dark:text-blue-400" />
                        <span class="text-sm font-medium text-blue-900 dark:text-blue-100">
                            {{ schoolContext.school?.name }}
                        </span>
                        <span class="text-xs text-blue-600 dark:text-blue-400">
                            ({{ schoolContext.membership?.role }})
                        </span>
                    </div>
                    <button @click="showSchoolSwitcher = true"
                        class="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 font-medium">
                        Switch School
                    </button>
                </div>
            </div>

            <!-- Main Content -->
            <main id="main-content-area" class="flex-1 overflow-x-hidden overflow-y-auto p-4 md:p-6 lg:p-8">
                <!-- Always render a container to prevent hydration mismatch -->
                <div class="min-h-64">
                    <ClientOnly>
                        <!-- Loading State -->
                        <template v-if="schoolContext.isLoading || (!schoolContext.school && !schoolContext.error)">
                            <div class="flex items-center justify-center h-64">
                                <div class="text-center">
                                    <Icon name="heroicons:arrow-path"
                                        class="h-8 w-8 animate-spin text-blue-600 mx-auto mb-2" />
                                    <p class="text-gray-600 dark:text-gray-400">Loading school data...</p>
                                </div>
                            </div>
                        </template>

                        <!-- Error State -->
                        <template v-else-if="schoolContext.error">
                            <div class="flex items-center justify-center h-64">
                                <div class="text-center">
                                    <Icon name="heroicons:exclamation-triangle"
                                        class="h-8 w-8 text-red-600 mx-auto mb-2" />
                                    <p class="text-red-600 dark:text-red-400 mb-2">{{ schoolContext.error }}</p>
                                    <button @click="refreshSchoolContext"
                                        class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 font-medium">
                                        Try Again
                                    </button>
                                </div>
                            </div>
                        </template>

                        <!-- Main Content -->
                        <template v-else-if="schoolContext.school">
                            <slot />
                        </template>

                        <!-- No School Access -->
                        <template v-else>
                            <div class="flex items-center justify-center h-64">
                                <div class="text-center">
                                    <Icon name="heroicons:building-office" class="h-8 w-8 text-gray-400 mx-auto mb-2" />
                                    <p class="text-gray-600 dark:text-gray-400 mb-2">No access to this school</p>
                                    <NuxtLink to="/login"
                                        class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 font-medium">
                                        Go to Login
                                    </NuxtLink>
                                </div>
                            </div>
                        </template>

                        <template #fallback>
                            <!-- SSR fallback - simple loading state -->
                            <div class="flex items-center justify-center h-64">
                                <div class="text-center">
                                    <Icon name="heroicons:arrow-path"
                                        class="h-8 w-8 animate-spin text-blue-600 mx-auto mb-2" />
                                    <p class="text-gray-600 dark:text-gray-400">Loading school data...</p>
                                </div>
                            </div>
                        </template>
                    </ClientOnly>
                </div>
            </main>
        </div>

        <!-- School Switcher Modal -->
        <!-- <UiModal v-model="showSchoolSwitcher" title="Switch School"> -->
        <div v-if="showSchoolSwitcher"
            class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Switch School</h3>
                <div class="space-y-4">
                    <p class="text-gray-600 dark:text-gray-400">
                        Select a school to switch to:
                    </p>
                    <div class="space-y-2">
                        <button v-for="school in userSchools" :key="school.id" @click="switchToSchool(school)"
                            class="w-full text-left p-3 rounded-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                            :class="{
                                'bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-800': school.id === schoolContext.school?.id
                            }">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="font-medium text-gray-900 dark:text-white">{{ school.name }}</p>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">{{ school.code }}</p>
                                </div>
                                <div v-if="school.id === schoolContext.school?.id"
                                    class="text-blue-600 dark:text-blue-400">
                                    <Icon name="heroicons:check" class="h-5 w-5" />
                                </div>
                            </div>
                        </button>
                    </div>
                    <button @click="showSchoolSwitcher = false"
                        class="mt-4 w-full bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 px-4 py-2 rounded-lg">
                        Close
                    </button>
                </div>
            </div>
        </div>
        <!-- </UiModal> -->

        <!-- Toast Container -->
        <ClientOnly>
            <UiToastContainer />
        </ClientOnly>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { School, SchoolContext } from '~/types/multiTenant'

// Mobile sidebar state
const isMobileSidebarOpen = ref(false)

// School switcher modal
const showSchoolSwitcher = ref(false)

// Use the new school context composable
const {
    schoolContext,
    initializeSchoolContext,
    preloadSchoolData,
    isSchoolContext
} = useSchoolContext()

// User schools (will be fetched from API)
const userSchools = ref<School[]>([])

// Composables
const router = useRouter()
const route = useRoute()

// Get school code from route params
const schoolCode = computed(() => {
    return route.params.school as string
})

// Mobile sidebar controls
const toggleMobileSidebar = () => {
    isMobileSidebarOpen.value = !isMobileSidebarOpen.value
}

const closeMobileSidebar = () => {
    isMobileSidebarOpen.value = false
}

// School context management
const refreshSchoolContext = async () => {
    try {
        // Use the new school context system
        await initializeSchoolContext()

        if (schoolContext.value.school) {
            console.log(`🏫 School context refreshed: ${schoolContext.value.school.name}`)
        }
    } catch (error) {
        console.error('Error refreshing school context:', error)
    }
}

// Initialize school context immediately but handle it properly for SSR
if (process.client) {
    // Client-side: initialize immediately
    initializeSchoolContext().catch(console.error)
} else {
    // Server-side: ensure consistent state
    onMounted(() => {
        initializeSchoolContext().catch(console.error)
    })
}

// Switch to different school
const switchToSchool = (school: School) => {
    showSchoolSwitcher.value = false

    // Navigate to the new school's subdomain
    const currentHost = window.location.host
    const baseDomain = currentHost.split('.').slice(1).join('.') // Remove subdomain
    const newUrl = `${window.location.protocol}//${school.code}.${baseDomain}${route.path}`

    window.location.href = newUrl
}

// Logout handler
const handleLogout = async () => {
    const supabaseClient = useSupabaseClient()

    if (!supabaseClient) {
        console.error('Supabase client is not available.')
        return
    }

    // Close mobile sidebar
    closeMobileSidebar()

    const { error } = await supabaseClient.auth.signOut()
    if (error) {
        console.error('Error logging out:', error.message)
    } else {
        // Redirect to the school's login page
        await router.push(`/${schoolCode.value}/auth/login`)
    }
}

// Watch for school code changes
watch(schoolCode, () => {
    refreshSchoolContext()
})

// Close mobile sidebar on route change
watch(() => route.path, () => {
    closeMobileSidebar()
})
</script>

<style scoped>
/* Scoped styles for the school layout */
</style>
