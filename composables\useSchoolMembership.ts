// Composable for managing school memberships
// Created: 2025-07-19
// Purpose: Centralized logic for creating and managing school memberships

import type { Database } from '~/types/supabase'

type SchoolMembership = Database['public']['Tables']['school_memberships']['Row']
type SchoolMembershipInsert = Database['public']['Tables']['school_memberships']['Insert']

export const useSchoolMembership = () => {
  const supabase = useSupabaseClient<Database>()

  /**
   * Extract school code from current subdomain
   */
  const getSchoolCodeFromSubdomain = (): string | null => {
    if (!process.client) return null
    
    const host = window.location.host
    const parts = host.split('.')
    
    // For development (schoolcode.localhost:3000)
    if (host.includes('localhost')) {
      if (parts.length > 1 && parts[0] !== 'localhost') {
        return parts[0].toLowerCase()
      }
      return null
    }
    
    // For production (schoolcode.domain.com)
    if (parts.length >= 3) {
      return parts[0].toLowerCase()
    }
    
    return null
  }

  /**
   * Check if user has membership in a specific school
   */
  const checkSchoolMembership = async (userId: string, schoolCode: string): Promise<{
    exists: boolean
    membership: SchoolMembership | null
    error: any
  }> => {
    try {
      // First get school ID
      const { data: school, error: schoolError } = await supabase
        .from('schools')
        .select('id')
        .eq('code', schoolCode.toLowerCase())
        .single()

      if (schoolError || !school) {
        return { exists: false, membership: null, error: schoolError }
      }

      // Check if membership exists
      const { data: membership, error: membershipError } = await supabase
        .from('school_memberships')
        .select('*')
        .eq('user_id', userId)
        .eq('school_id', school.id)
        .eq('status', 'active')
        .single()

      if (membershipError && membershipError.code !== 'PGRST116') {
        return { exists: false, membership: null, error: membershipError }
      }

      return { 
        exists: !!membership, 
        membership: membership || null, 
        error: null 
      }
    } catch (error) {
      return { exists: false, membership: null, error }
    }
  }

  /**
   * Create school membership for a user
   */
  const createSchoolMembership = async (
    userId: string, 
    schoolCode: string, 
    role: 'teacher' | 'admin' | 'supervisor' = 'teacher'
  ): Promise<{
    success: boolean
    membership: SchoolMembership | null
    error: any
  }> => {
    try {
      console.log(`[useSchoolMembership] Creating membership for user ${userId} in school ${schoolCode}`)

      // First get school ID
      const { data: school, error: schoolError } = await supabase
        .from('schools')
        .select('id, name')
        .eq('code', schoolCode.toLowerCase())
        .single()

      if (schoolError || !school) {
        console.error('[useSchoolMembership] Error finding school:', schoolError)
        return { success: false, membership: null, error: schoolError }
      }

      console.log(`[useSchoolMembership] Found school: ${school.name} (${school.id})`)

      // Check if membership already exists
      const { exists, membership: existingMembership } = await checkSchoolMembership(userId, schoolCode)
      
      if (exists && existingMembership) {
        console.log('[useSchoolMembership] Membership already exists:', existingMembership)
        return { success: true, membership: existingMembership, error: null }
      }

      // Create new membership
      const membershipData: SchoolMembershipInsert = {
        user_id: userId,
        school_id: school.id,
        role,
        status: 'active',
        joined_at: new Date().toISOString()
      }

      const { data: newMembership, error: membershipError } = await supabase
        .from('school_memberships')
        .insert(membershipData)
        .select()
        .single()

      if (membershipError) {
        console.error('[useSchoolMembership] Error creating membership:', membershipError)
        return { success: false, membership: null, error: membershipError }
      }

      console.log('[useSchoolMembership] School membership created successfully:', newMembership)
      return { success: true, membership: newMembership, error: null }

    } catch (error) {
      console.error('[useSchoolMembership] Unexpected error:', error)
      return { success: false, membership: null, error }
    }
  }

  /**
   * Ensure school membership exists for current user and school context
   * This is the main function to call after successful authentication
   */
  const ensureSchoolMembership = async (
    userId: string,
    role: 'teacher' | 'admin' | 'supervisor' = 'teacher'
  ): Promise<{
    success: boolean
    membership: SchoolMembership | null
    schoolCode: string | null
    error: any
  }> => {
    try {
      const schoolCode = getSchoolCodeFromSubdomain()
      
      if (!schoolCode || schoolCode === 'localhost' || schoolCode === 'www') {
        console.warn('[useSchoolMembership] No valid school code found for membership creation')
        return { success: false, membership: null, schoolCode: null, error: 'No valid school code' }
      }

      console.log(`[useSchoolMembership] Ensuring membership for user ${userId} in school ${schoolCode}`)

      const result = await createSchoolMembership(userId, schoolCode, role)
      
      return {
        success: result.success,
        membership: result.membership,
        schoolCode,
        error: result.error
      }
    } catch (error) {
      console.error('[useSchoolMembership] Error in ensureSchoolMembership:', error)
      return { success: false, membership: null, schoolCode: null, error }
    }
  }

  return {
    getSchoolCodeFromSubdomain,
    checkSchoolMembership,
    createSchoolMembership,
    ensureSchoolMembership
  }
}
