// Check Supabase OAuth URLs
// This will help us see what URLs Supabase is actually using

async function checkOAuthURLs() {
    console.log('=== OAuth URL Check ===');
    
    // What Supabase should accept
    const expectedURLs = [
        'http://localhost:3000/auth/callback',
        'https://localhost:3000/auth/callback'
    ];
    
    console.log('Expected redirect URLs that should be in Supabase:');
    expectedURLs.forEach(url => console.log(`  ✓ ${url}`));
    
    // What we're actually using
    console.log('\nActual URLs in use:');
    console.log(`  Current page: ${window.location.href}`);
    console.log(`  Origin: ${window.location.origin}`);
    console.log(`  Host: ${window.location.host}`);
    
    // Test URL construction
    const redirectUrl = 'http://localhost:3000/auth/callback';
    console.log(`\nConstructed redirect URL: ${redirectUrl}`);
    
    // Check if URL is properly formatted
    try {
        const urlObj = new URL(redirectUrl);
        console.log('URL validation:');
        console.log(`  Protocol: ${urlObj.protocol}`);
        console.log(`  Host: ${urlObj.host}`);
        console.log(`  Pathname: ${urlObj.pathname}`);
        console.log(`  Valid: ✓`);
    } catch (e) {
        console.error('URL validation failed:', e);
    }
    
    console.log('\n=== Instructions ===');
    console.log('In your Supabase Dashboard:');
    console.log('1. Go to Authentication → URL Configuration');
    console.log('2. Check "Redirect URLs" section');
    console.log('3. Ensure these URLs are added:');
    expectedURLs.forEach(url => console.log(`   - ${url}`));
    console.log('4. Save the configuration');
    console.log('\nThen try the OAuth flow again.');
}

checkOAuthURLs();
