<template>
  <div>
    <div class="flex flex-col items-center justify-center min-h-screen bg-transparent p-4">
      <div class="bg-white p-8 rounded-lg shadow-md text-center max-w-md w-full">
        <Icon name="svg-spinners:180-ring-with-bg" size="48" class="mb-6 text-primary mx-auto" />
        <h1 class="text-2xl font-semibold mb-4 text-gray-800">Sedang Memproses</h1>
        <p class="text-gray-600 mb-2">
          Sila tunggu sebentar sementara kami memeriksa status akaun anda...
        </p>
        <p v-if="statusMessage" class="text-sm text-gray-500 italic">{{ statusMessage }}</p>
        <p v-if="countdown > 0 && (errorRedirecting || profileNotFoundRedirecting)" class="text-sm text-gray-500 mt-2">
          Melanjutkan dalam {{ countdown }} saat...
        </p>
        <div v-if="showRetryButton" class="mt-6">
          <Button @click="manualRetry" variant="secondary" size="sm">
            Retry Check
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted as onCleanup, nextTick } from 'vue';
import { useSupabaseClient, navigateTo } from '#imports';
import type { Session, PostgrestError } from '@supabase/supabase-js';
import type { Database } from '~/types/supabase';
import Button from '~/components/ui/base/Button.vue';
import Icon from '~/components/ui/base/Icon.vue';

definePageMeta({
  layout: 'blank',
});

interface UserProfile {
  is_profile_complete: boolean;
  full_name: string | null;
}

const supabase = useSupabaseClient<Database>();

const MAX_RETRIES = 5;
const RETRY_DELAY_PROFILE_NOT_FOUND = 2000;
const RETRY_DELAY_EXCEPTION = 3000;
const QUERY_TIMEOUT_MS = 10000; // Increased to 10 seconds

const processingSignIn = ref(true);
const statusMessage = ref<string | null>('Memulakan proses...');
const isVerifyingProfile = ref(false); // Guard flag

const errorRedirectTimeoutId = ref<NodeJS.Timeout | null>(null);
const profileCheckRetryTimeoutId = ref<NodeJS.Timeout | null>(null);
const profileCheckAttempts = ref(0); // Added for retry logic

const countdown = ref(0);
const countdownIntervalId = ref<NodeJS.Timeout | null>(null);
const errorRedirecting = ref(false);
const profileNotFoundRedirecting = ref(false);
const showRetryButton = ref(false);

const startCountdownAndRedirect = async (path: string, seconds: number) => {
  countdown.value = seconds;
  if (countdownIntervalId.value) clearInterval(countdownIntervalId.value);
  countdownIntervalId.value = setInterval(async () => {
    countdown.value -= 1;
    if (countdown.value <= 0) {
      if (countdownIntervalId.value) clearInterval(countdownIntervalId.value);
      processingSignIn.value = false;
      isVerifyingProfile.value = false; // Reset guard on navigation
      await nextTick();
      await navigateTo(path, { replace: true });
    }
  }, 1000);
};

// Simplified profile check - just verify profile exists
const fetchProfileDataOnce = async (userId: string): Promise<{
  profile?: UserProfile | null;
  error?: PostgrestError | Error | null;
  timedOut?: boolean;
}> => {
  // console.log(`[confirm.vue] fetchProfileDataOnce: Querying profile for user ${userId}.`);
  statusMessage.value = `Memproses profail pengguna ${userId}...`;

  const profileQuery = supabase
    .from('profiles')
    .select('full_name')
    .eq('id', userId)
    .maybeSingle<UserProfile>();

  const queryTimeoutPromise = new Promise((_, rejectTimeout) =>
    setTimeout(() => rejectTimeout(new Error('Profile query timed out after ' + QUERY_TIMEOUT_MS + 'ms')), QUERY_TIMEOUT_MS)
  );

  try {
    // @ts-ignore
    const result = await Promise.race([profileQuery, queryTimeoutPromise]) as { data: UserProfile | null; error: PostgrestError | null; status: number; };
    // console.log('[confirm.vue] Supabase profile query result:', result);
    return { profile: result.data, error: result.error };
  } catch (e) {
    // console.warn(`[confirm.vue] Profile query timed out or other race error for user ${userId}.`, e);
    return { error: e as Error, timedOut: true };
  }
};

const manualRetry = async () => {
  // console.log('[confirm.vue] Manual retry initiated.');
  showRetryButton.value = false;
  errorRedirecting.value = false;
  profileNotFoundRedirecting.value = false;
  if (countdownIntervalId.value) clearInterval(countdownIntervalId.value);
  countdown.value = 0;
  statusMessage.value = 'Mencuba semula...';
  processingSignIn.value = true;

  supabase.auth.getSession().then(async ({ data: { session } }) => {
    if (session && session.user) {
      await handleSuccessfulSignIn(session);
    } else {
      statusMessage.value = 'Tiada sesi aktif. Mengubah hala ke halaman log masuk.';
      errorRedirecting.value = true;
      await startCountdownAndRedirect('/auth/login', 5); // Updated from /auth/google-auth
    }
  }).catch(async () => {
    statusMessage.value = 'Ralat untuk mendapatkan sesi gagal. Mengubah hala ke halaman log masuk.';
    errorRedirecting.value = true;
    await startCountdownAndRedirect('/auth/login', 5); // Updated from /auth/google-auth
  });
};

// Rewritten handleSuccessfulSignIn
const handleSuccessfulSignIn = async (session: Session | null) => {
  console.log('🔄 [confirm.vue] handleSuccessfulSignIn called');

  if (isVerifyingProfile.value) {
    console.log('⚠️ [confirm.vue] Profile verification already in progress, skipping');
    return;
  }
  isVerifyingProfile.value = true;

  clearAllTimers();
  errorRedirecting.value = false;
  profileNotFoundRedirecting.value = false;
  showRetryButton.value = false;
  countdown.value = 0;
  profileCheckAttempts.value = 0; // Reset attempts for this sign-in flow

  if (!session?.user?.id) {
    console.error('❌ [confirm.vue] No user ID in session');
    statusMessage.value = 'Ralat: Tiada ID pengguna dalam sesi. Mengubah hala ke halaman log masuk.';
    errorRedirecting.value = true;
    await startCountdownAndRedirect('/auth/login', 5);
    isVerifyingProfile.value = false; // Ensure reset before exiting
    return;
  }

  const userId = session.user.id;
  console.log('✅ [confirm.vue] Starting profile verification for user:', userId);
  statusMessage.value = 'Authentication successful. Verifying profile...';

  const attemptProfileVerification = async () => {
    if (!isVerifyingProfile.value && profileCheckAttempts.value > 0) {
      return;
    }

    profileCheckAttempts.value++;
    statusMessage.value = `Menyemak status profil (Percubaan ${profileCheckAttempts.value})...`;

    const { profile, error, timedOut } = await fetchProfileDataOnce(userId);

    if (!isVerifyingProfile.value) { // Check again in case of async gap and unmount/cancellation
      return;
    }

    if (error) {
      if (profileCheckAttempts.value < MAX_RETRIES) {
        statusMessage.value = timedOut ? `Proses tamat tempoh (Percubaan ${profileCheckAttempts.value}). Mencuba semula...` : `Ralat mendapatkan profil: ${(error as Error).message} (Percubaan ${profileCheckAttempts.value}). Mencuba semula...`;
        profileCheckRetryTimeoutId.value = setTimeout(attemptProfileVerification, timedOut ? RETRY_DELAY_PROFILE_NOT_FOUND : RETRY_DELAY_EXCEPTION);
      } else {
        statusMessage.value = 'Gagal mengesahkan profil selepas beberapa percubaan. Sila cuba log masuk semula.';
        errorRedirecting.value = true;
        showRetryButton.value = false; // Or true, depending on desired UX
        await startCountdownAndRedirect('/auth/login', 10);
        // isVerifyingProfile is reset by startCountdownAndRedirect
      }
      return;
    }

    if (!profile) {
      console.log('❌ [confirm.vue] Profile not found, attempting to create');

      // For Google OAuth users, try to create profile automatically
      if (profileCheckAttempts.value === 1) {
        console.log('🔧 [confirm.vue] First attempt - creating user profile');
        statusMessage.value = 'Creating user profile...';

        try {
          // Get current session to access user metadata
          const { data: { session } } = await supabase.auth.getSession()

          if (session?.user) {
            const user = session.user
            console.log('👤 [confirm.vue] User data for profile creation:', {
              id: user.id,
              email: user.email,
              user_metadata: user.user_metadata,
              identities: user.identities,
              app_metadata: user.app_metadata
            });

            // Try multiple sources for the full name - Google provides name in different fields
            const fullName = user.user_metadata?.full_name ||
              user.user_metadata?.name ||
              user.user_metadata?.display_name ||
              user.identities?.[0]?.identity_data?.full_name ||
              user.identities?.[0]?.identity_data?.name ||
              user.identities?.[0]?.identity_data?.display_name ||
              user.app_metadata?.full_name ||
              user.app_metadata?.name ||
              `${user.user_metadata?.given_name || ''} ${user.user_metadata?.family_name || ''}`.trim() ||
              `${user.identities?.[0]?.identity_data?.given_name || ''} ${user.identities?.[0]?.identity_data?.family_name || ''}`.trim() ||
              user.email?.split('@')[0] || // Fallback to email username
              'Teacher';

            console.log('📝 [confirm.vue] Extracted full name:', fullName);

            const profileData: Database['public']['Tables']['profiles']['Insert'] = {
              id: user.id,
              full_name: fullName,
              avatar_url: user.user_metadata?.avatar_url || user.identities?.[0]?.identity_data?.avatar_url || null,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            };

            console.log('💾 [confirm.vue] Profile data to update:', profileData);

            // Update the profile created by the trigger instead of inserting
            const { error: createError, data: updatedProfile } = await supabase
              .from('profiles')
              .update({
                full_name: fullName,
                avatar_url: profileData.avatar_url,
                updated_at: new Date().toISOString()
              })
              .eq('id', user.id)
              .select()
              .single()

            if (createError) {
              console.error('❌ [confirm.vue] Profile update error:', createError);
              // Continue with retry logic below
            } else {
              console.log('✅ [confirm.vue] Profile updated successfully:', updatedProfile);

              // Profile created successfully, now create school membership
              statusMessage.value = 'Creating school membership...';
              console.log('🏫 [confirm.vue] Starting school membership creation');

              try {
                const { ensureSchoolMembership } = useSchoolMembership()
                const membershipResult = await ensureSchoolMembership(user.id, 'teacher')
                console.log('🏫 [confirm.vue] School membership result:', membershipResult);
              } catch (membershipErr) {
                console.error('❌ [confirm.vue] School membership error:', membershipErr);
                // Continue anyway - profile exists
              }

              // Profile created successfully, redirect via home route for proper middleware handling
              console.log('✅ [confirm.vue] Setup complete, redirecting to dashboard');
              statusMessage.value = 'Setup complete. Redirecting...';
              isVerifyingProfile.value = false;
              await nextTick();
              await navigateTo('/', { replace: true });
              return;
            }
          } else {
            console.error('❌ [confirm.vue] No user in session for profile creation');
          }
        } catch (error) {
          console.error('❌ [confirm.vue] Profile creation error:', error);
          // Continue with retry logic below
        }
      } else {
        console.log(`🔄 [confirm.vue] Profile creation attempt ${profileCheckAttempts.value} (not first attempt)`);
      }

      if (profileCheckAttempts.value < MAX_RETRIES) {
        statusMessage.value = `Profile data not yet available. Retrying (Attempt ${profileCheckAttempts.value})...`;
        profileCheckRetryTimeoutId.value = setTimeout(attemptProfileVerification, RETRY_DELAY_PROFILE_NOT_FOUND);
      } else {
        statusMessage.value = 'Profile setup may be needed or verification timed out. Redirecting...';
        profileNotFoundRedirecting.value = true;
        await startCountdownAndRedirect('/auth/login', 5);
        // isVerifyingProfile is reset by startCountdownAndRedirect
      }
      return;
    }

    clearAllTimers(); // Clear any pending retry timers

    // Check if profile needs name update (if full_name is null/empty)
    if (!profile.full_name || profile.full_name.trim() === '') {
      statusMessage.value = 'Updating profile with Google information...';

      try {
        // Get current session to access user metadata
        const { data: { session } } = await supabase.auth.getSession()

        if (session?.user) {
          const user = session.user

          const fullName = user.user_metadata?.full_name ||
            user.user_metadata?.name ||
            user.user_metadata?.display_name ||
            user.identities?.[0]?.identity_data?.full_name ||
            user.identities?.[0]?.identity_data?.name ||
            user.identities?.[0]?.identity_data?.display_name ||
            user.app_metadata?.full_name ||
            user.app_metadata?.name ||
            `${user.user_metadata?.given_name || ''} ${user.user_metadata?.family_name || ''}`.trim() ||
            `${user.identities?.[0]?.identity_data?.given_name || ''} ${user.identities?.[0]?.identity_data?.family_name || ''}`.trim() ||
            user.email?.split('@')[0] || // Fallback to email username
            null;

          if (fullName) {
            // Update the existing profile with the extracted name
            await supabase
              .from('profiles')
              .update({
                full_name: fullName,
                updated_at: new Date().toISOString()
              })
              .eq('id', userId)
          }
        }
      } catch (nameUpdateError) {
        // Continue anyway - profile exists
      }
    }

    // Profile exists - now ensure school membership exists
    console.log('✅ [confirm.vue] Profile exists, checking school membership');
    statusMessage.value = 'Checking school membership...';

    try {
      const { ensureSchoolMembership } = useSchoolMembership()
      const membershipResult = await ensureSchoolMembership(userId, 'teacher')
      console.log('🏫 [confirm.vue] School membership check result:', membershipResult);
    } catch (membershipError) {
      console.error('❌ [confirm.vue] School membership check error:', membershipError);
      // Continue anyway - profile exists
    }

    // Profile exists and membership handled - redirect via home route for proper middleware handling
    console.log('✅ [confirm.vue] Profile complete, redirecting to dashboard');
    statusMessage.value = 'Profail lengkap. Mengubah hala...';
    isVerifyingProfile.value = false;
    await nextTick();
    await navigateTo('/', { replace: true });
  };

  await attemptProfileVerification(); // Start the first attempt
  // isVerifyingProfile is managed by attemptProfileVerification or startCountdownAndRedirect
};

onMounted(() => {
  console.log('🚀 [confirm.vue] Component mounted, starting OAuth confirmation flow');

  // Check if we're on the main domain and need to redirect to school subdomain
  const currentHost = window.location.host;
  const isMainDomain = currentHost === 'localhost:3000';

  console.log('🌐 [confirm.vue] Current host:', currentHost);
  console.log('🌐 [confirm.vue] Is main domain:', isMainDomain);

  if (isMainDomain) {
    // Check if we have a stored school code from OAuth flow
    const storedSchoolCode = localStorage.getItem('oauth_school_code');
    console.log('💾 [confirm.vue] Stored school code from localStorage:', storedSchoolCode);

    if (storedSchoolCode) {
      // Redirect to school subdomain with the current URL parameters
      const schoolSubdomain = `${storedSchoolCode}.localhost:3000`;
      const currentUrl = new URL(window.location.href);
      const redirectUrl = `http://${schoolSubdomain}${currentUrl.pathname}${currentUrl.search}${currentUrl.hash}`;

      console.log('🔄 [confirm.vue] Redirecting to school subdomain:', redirectUrl);

      // Clear the stored school code
      localStorage.removeItem('oauth_school_code');
      console.log('🗑️ [confirm.vue] Cleared stored school code from localStorage');

      // Redirect to school subdomain
      window.location.href = redirectUrl;
      return;
    } else {
      console.warn('⚠️ [confirm.vue] No stored school code found for main domain redirect');
    }
  }

  // Immediately check the session on mount
  console.log('🔍 [confirm.vue] Checking session...');
  supabase.auth.getSession().then(async ({ data: { session } }) => {
    if (session && session.user) {
      console.log('✅ [confirm.vue] Session found, user ID:', session.user.id);
      console.log('👤 [confirm.vue] User metadata:', session.user.user_metadata);
      console.log('🔗 [confirm.vue] User identities:', session.user.identities);
      await handleSuccessfulSignIn(session);
    } else {
      console.warn('⚠️ [confirm.vue] No session found, redirecting to login');
      errorRedirecting.value = true;
      await startCountdownAndRedirect('/auth/login', 5);
    }
  }).catch(async (error) => {
    console.error('❌ [confirm.vue] Error fetching session:', error);
    statusMessage.value = 'Error fetching session. Redirecting to sign-in.';
    errorRedirecting.value = true;
    await startCountdownAndRedirect('/auth/login', 5);
  });
});

onCleanup(() => {
  // console.log('[confirm.vue] Component unmounted. Cleaning up timers...');
  clearAllTimers();
});

const clearAllTimers = () => {
  if (countdownIntervalId.value) clearInterval(countdownIntervalId.value);
  if (errorRedirectTimeoutId.value) clearTimeout(errorRedirectTimeoutId.value);
  if (profileCheckRetryTimeoutId.value) clearTimeout(profileCheckRetryTimeoutId.value);
  countdownIntervalId.value = null;
  errorRedirectTimeoutId.value = null;
  profileCheckRetryTimeoutId.value = null;
  // console.log('[confirm.vue] Cleared all timers.');
};
</script>

<style scoped>
/* Add any component-specific styles here */
</style>
