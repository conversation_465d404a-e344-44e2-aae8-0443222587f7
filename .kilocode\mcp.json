{"mcpServers": {"context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"], "env": {"DEFAULT_MINIMUM_TOKENS": ""}}, "puppeteer": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-puppeteer"]}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"]}, "stripe": {"command": "npx", "args": ["-y", "@stripe/mcp", "--tools=all", "--api-key=sk_test_51RkFYfGKbo6Mo0foApiufjoVBpIEbbeZhsA0W90vN6MHoyKyfaCrEpa2cy5GpTjC76hLIfg2DDclWdSeYnR7efzP00aQLGTUDU"]}}}