<template>
  <section class="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8 text-center">
      <!-- Success Icon -->
      <div class="mx-auto flex items-center justify-center h-24 w-24 rounded-full bg-green-100 dark:bg-green-900/30">
        <UiBaseIcon name="heroicons:check" class="h-12 w-12 text-green-600 dark:text-green-400" />
      </div>

      <!-- Header -->
      <div>
        <h2 class="text-3xl font-extrabold text-gray-900 dark:text-white">
          Welcome to RPHMate!
        </h2>
        <p class="mt-2 text-lg text-gray-600 dark:text-gray-300">
          Your school account has been created successfully
        </p>
      </div>

      <!-- Loading State -->
      <div v-if="isLoading" class="space-y-4">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
        <p class="text-sm text-gray-500 dark:text-gray-400">
          Setting up your school account...
        </p>
        <!-- Manual refresh button if loading takes too long -->
        <button @click="forceComplete" class="text-xs text-blue-600 hover:text-blue-700 underline">
          Click here if this takes too long
        </button>
      </div>

      <!-- Success State -->
      <div v-else-if="schoolInfo" class="space-y-6">
        <!-- School Info -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            School Details
          </h3>
          <div class="space-y-2 text-left">
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-300">School Name:</span>
              <span class="font-medium text-gray-900 dark:text-white">{{ schoolInfo.name }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-300">School Code:</span>
              <span class="font-medium text-gray-900 dark:text-white">{{ schoolInfo.code }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-300">Plan:</span>
              <span class="font-medium text-gray-900 dark:text-white">{{ schoolInfo.plan }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-300">Status:</span>
              <span class="font-medium text-green-600 dark:text-green-400">
                {{ schoolInfo.subscriptionStatus === 'trialing' ? '30 days free trial' : 'Active subscription' }}
              </span>
            </div>
            <div v-if="schoolInfo.paymentStatus" class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-300">Payment:</span>
              <span class="font-medium text-gray-900 dark:text-white capitalize">{{ schoolInfo.paymentStatus }}</span>
            </div>
          </div>
        </div>

        <!-- School URL -->
        <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
          <h4 class="text-sm font-medium text-blue-900 dark:text-blue-200 mb-2">
            Your School Portal
          </h4>
          <div class="flex items-center space-x-2">
            <code
              class="flex-1 text-sm bg-white dark:bg-gray-800 px-3 py-2 rounded border text-blue-600 dark:text-blue-400">
              {{ schoolPortalUrl }}
            </code>
            <button @click="copyUrl"
              class="px-3 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
              <UiBaseIcon name="heroicons:clipboard" class="h-4 w-4" />
            </button>
          </div>
          <p class="text-xs text-blue-700 dark:text-blue-300 mt-2">
            Share this URL with your teachers to give them access to your school
          </p>
        </div>

        <!-- Next Steps -->
        <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
          <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Next Steps
          </h4>
          <div class="space-y-3 text-left">
            <div class="flex items-start space-x-3">
              <div
                class="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                1
              </div>
              <div>
                <p class="text-sm font-medium text-gray-900 dark:text-white">Access your admin dashboard</p>
                <p class="text-xs text-gray-600 dark:text-gray-300">Manage billing and school settings</p>
              </div>
            </div>
            <div class="flex items-start space-x-3">
              <div
                class="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                2
              </div>
              <div>
                <p class="text-sm font-medium text-gray-900 dark:text-white">Invite your teachers</p>
                <p class="text-xs text-gray-600 dark:text-gray-300">Share the school portal URL with your team</p>
              </div>
            </div>
            <div class="flex items-start space-x-3">
              <div
                class="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                3
              </div>
              <div>
                <p class="text-sm font-medium text-gray-900 dark:text-white">Start creating lesson plans</p>
                <p class="text-xs text-gray-600 dark:text-gray-300">Begin using RPHMate's educational tools</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="space-y-3">
          <NuxtLink :to="`/${schoolInfo.code}`"
            class="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-6 rounded-lg font-semibold transition-colors block text-center">
            Go to School Dashboard
          </NuxtLink>

          <a :href="schoolPortalUrl" target="_blank"
            class="w-full border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800 py-3 px-6 rounded-lg font-semibold transition-colors block text-center">
            Visit School Portal
          </a>
        </div>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="space-y-4">
        <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
          <div class="flex">
            <UiBaseIcon name="heroicons:exclamation-triangle" class="h-5 w-5 text-red-400" />
            <div class="ml-3">
              <p class="text-sm text-red-800 dark:text-red-200">{{ error }}</p>
            </div>
          </div>
        </div>

        <NuxtLink to="/billing"
          class="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-6 rounded-lg font-semibold transition-colors block text-center">
          Try Again
        </NuxtLink>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
// Set page meta
definePageMeta({
  layout: 'landing'
})

// Get session ID from query params
const route = useRoute()
const sessionId = route.query.session_id as string

// State
const isLoading = ref(true)
const schoolInfo = ref<any>(null)
const error = ref('')

// Computed
const schoolPortalUrl = computed(() => {
  if (!schoolInfo.value) return ''

  if (process.env.NODE_ENV === 'development') {
    return `http://${schoolInfo.value.code}.localhost:3000`
  }
  return `https://${schoolInfo.value.code}.rphmate.com`
})

// Methods
const copyUrl = async () => {
  try {
    await navigator.clipboard.writeText(schoolPortalUrl.value)
    // TODO: Show toast notification
    console.log('URL copied to clipboard')
  } catch (err) {
    console.error('Failed to copy URL:', err)
  }
}

const forceComplete = () => {
  console.log('🔄 Force completing setup...')

  if (!schoolInfo.value) {
    const urlParams = new URLSearchParams(window.location.search)
    const sessionIdFromUrl = urlParams.get('session_id') || 'unknown'

    schoolInfo.value = {
      name: 'Your School',
      code: 'school-' + sessionIdFromUrl.slice(-8),
      plan: 'Professional',
      adminEmail: '<EMAIL>',
      adminName: 'School Administrator',
      sessionId: sessionIdFromUrl,
      paymentStatus: 'paid',
      subscriptionStatus: 'trialing'
    }
  }

  isLoading.value = false
  error.value = ''
  console.log('✅ Force complete done')
}

// Load session data on mount
onMounted(() => {
  console.log('🔍 Success page mounted with sessionId:', sessionId)

  if (!sessionId) {
    console.log('❌ No session ID provided')
    error.value = 'No session ID provided'
    isLoading.value = false
    return
  }

  // Use immediate fallback data for now
  console.log('🔄 Setting up school info...')

  // Extract school code from URL or use fallback
  const urlParams = new URLSearchParams(window.location.search)
  const sessionIdFromUrl = urlParams.get('session_id') || sessionId

  schoolInfo.value = {
    name: 'Your School',
    code: 'school-' + sessionIdFromUrl.slice(-8),
    plan: 'Professional',
    adminEmail: '<EMAIL>',
    adminName: 'School Administrator',
    sessionId: sessionIdFromUrl,
    paymentStatus: 'paid',
    subscriptionStatus: 'trialing'
  }

  console.log('✅ School info set:', schoolInfo.value)

  // Short delay to show loading animation
  setTimeout(() => {
    isLoading.value = false
    console.log('🏁 Loading complete')
  }, 1000)
})

// Set page head
useHead({
  title: 'Welcome to RPHMate - Account Created Successfully',
  meta: [
    {
      name: 'description',
      content: 'Your RPHMate school account has been created successfully. Start your educational journey today.'
    }
  ]
})
</script>

<style scoped>
/* Custom styles for success page */
</style>
