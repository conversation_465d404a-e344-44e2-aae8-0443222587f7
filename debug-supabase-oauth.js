// Supabase OAuth Debug Tool
// Run this in browser console to debug OAuth issues

async function debugSupabaseOAuth() {
    console.log('=== Supabase OAuth Debug ===\n');
    
    // Get Supabase client
    const { createClient } = await import('@supabase/supabase-js');
    
    const supabaseUrl = 'https://nhgyywlfopodxomxbegx.supabase.co';
    const supabaseAnonKey = 'your-anon-key'; // Replace with actual key
    
    const client = createClient(supabaseUrl, supabaseAnonKey, {
        auth: {
            debug: true,
            flowType: 'pkce',
            autoRefreshToken: true,
            persistSession: true,
            detectSessionInUrl: true
        }
    });
    
    console.log('1. Current URL:', window.location.href);
    console.log('2. URL Parameters:');
    
    const urlParams = new URLSearchParams(window.location.search);
    for (const [key, value] of urlParams) {
        console.log(`   ${key}: ${value}`);
    }
    
    console.log('\n3. Session Check:');
    try {
        const { data: { session }, error } = await client.auth.getSession();
        console.log('   Session:', session ? 'EXISTS' : 'NULL');
        console.log('   Error:', error);
        
        if (session) {
            console.log('   User ID:', session.user.id);
            console.log('   Email:', session.user.email);
            console.log('   Provider:', session.user.app_metadata.provider);
        }
    } catch (err) {
        console.error('   Session check failed:', err);
    }
    
    console.log('\n4. Testing OAuth Initiation:');
    console.log('   Redirect URL: http://localhost:3000/auth/callback');
    
    // Test OAuth URL generation
    try {
        const { data, error } = await client.auth.signInWithOAuth({
            provider: 'google',
            options: {
                redirectTo: 'http://localhost:3000/auth/callback',
                queryParams: {
                    prompt: 'select_account'
                }
            }
        });
        
        console.log('   OAuth Data:', data);
        console.log('   OAuth Error:', error);
    } catch (err) {
        console.error('   OAuth test failed:', err);
    }
    
    console.log('\n=== Debug Complete ===');
}

// Run the debug
debugSupabaseOAuth().catch(console.error);
