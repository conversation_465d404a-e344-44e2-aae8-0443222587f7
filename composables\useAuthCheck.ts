import { useRouter } from '#imports'
import { useSupabase } from './useSupabase'

export const useAuthCheck = () => {
  const { client } = useSupabase()
  const router = useRouter()

  const checkLoggedIn = async (): Promise<boolean> => {
    try {
      const { data: { session } } = await client.auth.getSession()
      return !!session
    } catch (error) {
      console.error('Error checking auth status:', error)
      return false
    }
  }

  const redirectIfLoggedIn = async (redirectTo: string = '/dashboard') => {
    const isLoggedIn = await checkLoggedIn()
    if (isLoggedIn) {
      await router.push(redirectTo)
      return true
    }
    return false
  }

  return {
    checkLoggedIn,
    redirectIfLoggedIn
  }
}