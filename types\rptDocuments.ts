import type { Database } from './supabase';

// Database types from Supabase
export type RptDocumentRow = Database['public']['Tables']['rpt_documents']['Row'];
export type RptDocumentInsert = Database['public']['Tables']['rpt_documents']['Insert'];
export type RptDocumentUpdate = Database['public']['Tables']['rpt_documents']['Update'];

// Enhanced RPT Document interface for application use
export interface RptDocument {
  id: string;
  user_id: string;
  class_id: string;
  subject_id: string;
  class_name: string;
  subject_name: string;
  file_name: string;
  storage_file_path: string;
  file_mime_type: string;
  file_size_bytes: number;
  created_at: string | null;
  updated_at: string | null;
}

// Input interface for creating new RPT documents
export interface RptDocumentInput {
  class_id: string;
  subject_id: string;
  class_name: string;
  subject_name: string;
}

// Interface for RPT document with class-subject information
export interface RptDocumentWithClassSubject extends RptDocument {
  subject_abbreviation?: string;
  student_count?: number;
}

// Interface for RPT upload form data
export interface RptUploadFormData {
  class_subject_id: string; // Combined class_id and subject_id for selection
  file: File | null;
}

// Interface for class-subject selection options
export interface ClassSubjectOption {
  value: string; // Combined class_id_subject_id
  label: string; // Display name like "Tahun 1 Amanah - Matematik"
  class_id: string;
  subject_id: string;
  class_name: string;
  subject_name: string;
  subject_abbreviation?: string;
}

// Status interface for RPT documents per class-subject
export interface RptStatus {
  class_id: string;
  subject_id: string;
  class_name: string;
  subject_name: string;
  subject_abbreviation?: string;
  has_rpt: boolean;
  rpt_document?: RptDocument;
}

// File type validation
export const SUPPORTED_RPT_FILE_TYPES = [
  'application/pdf',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
  'application/vnd.openxmlformats-officedocument.presentationml.presentation', // .pptx
  'application/msword', // .doc
  'application/vnd.ms-excel', // .xls
  'application/vnd.ms-powerpoint', // .ppt
] as const;

export const SUPPORTED_RPT_FILE_EXTENSIONS = [
  '.pdf',
  '.docx',
  '.xlsx',
  '.pptx',
  '.doc',
  '.xls',
  '.ppt',
] as const;

// File size limit (10MB)
export const MAX_RPT_FILE_SIZE_BYTES = 10 * 1024 * 1024;
export const MAX_RPT_FILE_SIZE_MB = 10;

// File type helpers
export function isValidRptFileType(mimeType: string): boolean {
  return SUPPORTED_RPT_FILE_TYPES.includes(mimeType as any);
}

export function getFileTypeIcon(mimeType: string): string {
  if (mimeType === 'application/pdf') return 'mdi:file-pdf';
  if (mimeType.includes('word') || mimeType.includes('document')) return 'mdi:file-word';
  if (mimeType.includes('sheet') || mimeType.includes('excel')) return 'mdi:file-excel';
  if (mimeType.includes('presentation') || mimeType.includes('powerpoint')) return 'mdi:file-powerpoint';
  return 'mdi:file-document';
}

export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

export function getPreviewType(mimeType: string): 'office' | 'image' | null {
  if (mimeType === 'application/pdf') return 'office';
  if (mimeType.includes('word') || mimeType.includes('document')) return 'office';
  if (mimeType.includes('sheet') || mimeType.includes('excel')) return 'office';
  if (mimeType.includes('presentation') || mimeType.includes('powerpoint')) return 'office';
  if (mimeType.startsWith('image/')) return 'image';
  return null;
}
