export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instanciate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "12.2.3 (519615d)"
  }
  public: {
    Tables: {
      academic_calendar_documents: {
        Row: {
          created_at: string
          file_mime_type: string
          file_name: string
          file_size_bytes: number
          id: string
          storage_file_path: string
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          file_mime_type: string
          file_name: string
          file_size_bytes: number
          id?: string
          storage_file_path: string
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          file_mime_type?: string
          file_name?: string
          file_size_bytes?: number
          id?: string
          storage_file_path?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      annual_calendar_events: {
        Row: {
          category: string
          color: string | null
          created_at: string
          description: string | null
          end_date: string | null
          id: string
          location: string | null
          start_date: string
          title: string
          updated_at: string
          user_id: string
        }
        Insert: {
          category: string
          color?: string | null
          created_at?: string
          description?: string | null
          end_date?: string | null
          id?: string
          location?: string | null
          start_date: string
          title: string
          updated_at?: string
          user_id: string
        }
        Update: {
          category?: string
          color?: string | null
          created_at?: string
          description?: string | null
          end_date?: string | null
          id?: string
          location?: string | null
          start_date?: string
          title?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      coupon_usage: {
        Row: {
          coupon_id: string | null
          id: string
          school_id: string | null
          used_at: string | null
          user_id: string | null
        }
        Insert: {
          coupon_id?: string | null
          id?: string
          school_id?: string | null
          used_at?: string | null
          user_id?: string | null
        }
        Update: {
          coupon_id?: string | null
          id?: string
          school_id?: string | null
          used_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "coupon_usage_coupon_id_fkey"
            columns: ["coupon_id"]
            isOneToOne: false
            referencedRelation: "coupons"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "coupon_usage_school_id_fkey"
            columns: ["school_id"]
            isOneToOne: false
            referencedRelation: "schools"
            referencedColumns: ["id"]
          },
        ]
      }
      coupons: {
        Row: {
          code: string
          created_at: string | null
          created_by: string | null
          description: string | null
          discount_type: string
          discount_value: number
          expires_at: string | null
          id: string
          is_active: boolean | null
          trial_days: number | null
          updated_at: string | null
          usage_limit: number | null
          used_count: number | null
        }
        Insert: {
          code: string
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          discount_type: string
          discount_value: number
          expires_at?: string | null
          id?: string
          is_active?: boolean | null
          trial_days?: number | null
          updated_at?: string | null
          usage_limit?: number | null
          used_count?: number | null
        }
        Update: {
          code?: string
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          discount_type?: string
          discount_value?: number
          expires_at?: string | null
          id?: string
          is_active?: boolean | null
          trial_days?: number | null
          updated_at?: string | null
          usage_limit?: number | null
          used_count?: number | null
        }
        Relationships: []
      }
      dskp_documents: {
        Row: {
          class_id: string
          class_name: string
          created_at: string | null
          file_mime_type: string
          file_name: string
          file_size_bytes: number
          id: string
          storage_file_path: string
          subject_id: string
          subject_name: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          class_id: string
          class_name: string
          created_at?: string | null
          file_mime_type: string
          file_name: string
          file_size_bytes: number
          id?: string
          storage_file_path: string
          subject_id: string
          subject_name: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          class_id?: string
          class_name?: string
          created_at?: string | null
          file_mime_type?: string
          file_name?: string
          file_size_bytes?: number
          id?: string
          storage_file_path?: string
          subject_id?: string
          subject_name?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "dskp_documents_subject_id_fkey"
            columns: ["subject_id"]
            isOneToOne: false
            referencedRelation: "subjects"
            referencedColumns: ["id"]
          },
        ]
      }
      failed_payments: {
        Row: {
          admin_data: Json
          created_at: string
          currency: string | null
          error_details: Json | null
          id: string
          last_retry_at: string | null
          max_retries: number | null
          next_retry_at: string | null
          payment_amount: number
          resolution_notes: string | null
          resolved_at: string | null
          resolved_by: string | null
          retry_count: number | null
          school_data: Json
          status: string | null
          stripe_customer_id: string | null
          stripe_session_id: string
          updated_at: string
        }
        Insert: {
          admin_data: Json
          created_at?: string
          currency?: string | null
          error_details?: Json | null
          id?: string
          last_retry_at?: string | null
          max_retries?: number | null
          next_retry_at?: string | null
          payment_amount: number
          resolution_notes?: string | null
          resolved_at?: string | null
          resolved_by?: string | null
          retry_count?: number | null
          school_data: Json
          status?: string | null
          stripe_customer_id?: string | null
          stripe_session_id: string
          updated_at?: string
        }
        Update: {
          admin_data?: Json
          created_at?: string
          currency?: string | null
          error_details?: Json | null
          id?: string
          last_retry_at?: string | null
          max_retries?: number | null
          next_retry_at?: string | null
          payment_amount?: number
          resolution_notes?: string | null
          resolved_at?: string | null
          resolved_by?: string | null
          retry_count?: number | null
          school_data?: Json
          status?: string | null
          stripe_customer_id?: string | null
          stripe_session_id?: string
          updated_at?: string
        }
        Relationships: []
      }
      global_settings: {
        Row: {
          created_at: string | null
          id: string
          last_updated_at: string | null
          last_updated_by: string | null
          school_id: string | null
          setting_key: string
          setting_value: Json
        }
        Insert: {
          created_at?: string | null
          id?: string
          last_updated_at?: string | null
          last_updated_by?: string | null
          school_id?: string | null
          setting_key: string
          setting_value: Json
        }
        Update: {
          created_at?: string | null
          id?: string
          last_updated_at?: string | null
          last_updated_by?: string | null
          school_id?: string | null
          setting_key?: string
          setting_value?: Json
        }
        Relationships: []
      }
      items: {
        Row: {
          created_at: string | null
          description: string | null
          id: string
          title: string | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          id?: string
          title?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          id?: string
          title?: string | null
        }
        Relationships: []
      }
      jadual_pencerapan: {
        Row: {
          class_subject_id: string
          created_at: string | null
          id: string
          notes: string | null
          observation_date: string
          observer_name: string
          observer_position: string
          status: string | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          class_subject_id: string
          created_at?: string | null
          id?: string
          notes?: string | null
          observation_date: string
          observer_name: string
          observer_position: string
          status?: string | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          class_subject_id?: string
          created_at?: string | null
          id?: string
          notes?: string | null
          observation_date?: string
          observer_name?: string
          observer_position?: string
          status?: string | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      lesson_plan_detailed_reflections: {
        Row: {
          created_at: string
          id: string
          lesson_plan_id: string
          overall_rating: number
          periods_with_custom_data: number
          reflections: Json
          total_periods: number
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          lesson_plan_id: string
          overall_rating?: number
          periods_with_custom_data?: number
          reflections?: Json
          total_periods?: number
          updated_at?: string
          user_id?: string
        }
        Update: {
          created_at?: string
          id?: string
          lesson_plan_id?: string
          overall_rating?: number
          periods_with_custom_data?: number
          reflections?: Json
          total_periods?: number
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "lesson_plan_detailed_reflections_lesson_plan_id_fkey"
            columns: ["lesson_plan_id"]
            isOneToOne: false
            referencedRelation: "lesson_plans"
            referencedColumns: ["id"]
          },
        ]
      }
      lesson_plans: {
        Row: {
          class_subject_ids: string[]
          created_at: string
          days_selected: string[]
          file_mime_type: string
          file_name: string
          file_size_bytes: number
          id: string
          school_id: string | null
          storage_file_path: string
          user_id: string
          week_id: string | null
          week_label: string
        }
        Insert: {
          class_subject_ids: string[]
          created_at?: string
          days_selected: string[]
          file_mime_type: string
          file_name: string
          file_size_bytes: number
          id?: string
          school_id?: string | null
          storage_file_path: string
          user_id: string
          week_id?: string | null
          week_label?: string
        }
        Update: {
          class_subject_ids?: string[]
          created_at?: string
          days_selected?: string[]
          file_mime_type?: string
          file_name?: string
          file_size_bytes?: number
          id?: string
          school_id?: string | null
          storage_file_path?: string
          user_id?: string
          week_id?: string | null
          week_label?: string
        }
        Relationships: [
          {
            foreignKeyName: "lesson_plans_school_id_fkey"
            columns: ["school_id"]
            isOneToOne: false
            referencedRelation: "schools"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "lesson_plans_week_id_fkey"
            columns: ["week_id"]
            isOneToOne: false
            referencedRelation: "rph_weeks"
            referencedColumns: ["id"]
          },
        ]
      }
      observation_schedules: {
        Row: {
          class_subject_id: string
          created_at: string
          id: string
          notes: string | null
          observation_date: string
          observer_name: string
          observer_position: string
          school_id: string | null
          status: string
          updated_at: string
          user_id: string
        }
        Insert: {
          class_subject_id: string
          created_at?: string
          id?: string
          notes?: string | null
          observation_date: string
          observer_name: string
          observer_position: string
          school_id?: string | null
          status?: string
          updated_at?: string
          user_id: string
        }
        Update: {
          class_subject_id?: string
          created_at?: string
          id?: string
          notes?: string | null
          observation_date?: string
          observer_name?: string
          observer_position?: string
          school_id?: string | null
          status?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "observation_schedules_school_id_fkey"
            columns: ["school_id"]
            isOneToOne: false
            referencedRelation: "schools"
            referencedColumns: ["id"]
          },
        ]
      }
      pre_billing: {
        Row: {
          admin_email: string
          admin_full_name: string | null
          billing_complete: boolean | null
          created_at: string
          email_verified: boolean | null
          expires_at: string
          id: string
          is_google_signup: boolean | null
          phase_completed: number | null
          school_address: string | null
          school_code: string | null
          school_name: string | null
          selected_plan: string | null
          session_token: string
          updated_at: string
          user_id: string | null
        }
        Insert: {
          admin_email: string
          admin_full_name?: string | null
          billing_complete?: boolean | null
          created_at?: string
          email_verified?: boolean | null
          expires_at?: string
          id?: string
          is_google_signup?: boolean | null
          phase_completed?: number | null
          school_address?: string | null
          school_code?: string | null
          school_name?: string | null
          selected_plan?: string | null
          session_token: string
          updated_at?: string
          user_id?: string | null
        }
        Update: {
          admin_email?: string
          admin_full_name?: string | null
          billing_complete?: boolean | null
          created_at?: string
          email_verified?: boolean | null
          expires_at?: string
          id?: string
          is_google_signup?: boolean | null
          phase_completed?: number | null
          school_address?: string | null
          school_code?: string | null
          school_name?: string | null
          selected_plan?: string | null
          session_token?: string
          updated_at?: string
          user_id?: string | null
        }
        Relationships: []
      }
      profiles: {
        Row: {
          academic_qualifications: Json | null
          appointment_date: string | null
          avatar_url: string | null
          class_subjects: Json | null
          created_at: string | null
          date_of_birth: string | null
          epf_number: string | null
          file_number: string | null
          full_name: string | null
          gender: string | null
          ic_number: string | null
          id: string
          income_tax_number: string | null
          is_profile_complete: boolean | null
          is_school_admin: boolean | null
          options: Json | null
          pensionable_position_date: string | null
          position_confirmation_date: string | null
          religion: string | null
          retirement_date: string | null
          role: Json | null
          salary_number: string | null
          spp_reference_number: string | null
          teacher_type: string | null
          teaching_days_mode: string | null
          time_slots: Json | null
          updated_at: string | null
          username: string | null
        }
        Insert: {
          academic_qualifications?: Json | null
          appointment_date?: string | null
          avatar_url?: string | null
          class_subjects?: Json | null
          created_at?: string | null
          date_of_birth?: string | null
          epf_number?: string | null
          file_number?: string | null
          full_name?: string | null
          gender?: string | null
          ic_number?: string | null
          id: string
          income_tax_number?: string | null
          is_profile_complete?: boolean | null
          is_school_admin?: boolean | null
          options?: Json | null
          pensionable_position_date?: string | null
          position_confirmation_date?: string | null
          religion?: string | null
          retirement_date?: string | null
          role?: Json | null
          salary_number?: string | null
          spp_reference_number?: string | null
          teacher_type?: string | null
          teaching_days_mode?: string | null
          time_slots?: Json | null
          updated_at?: string | null
          username?: string | null
        }
        Update: {
          academic_qualifications?: Json | null
          appointment_date?: string | null
          avatar_url?: string | null
          class_subjects?: Json | null
          created_at?: string | null
          date_of_birth?: string | null
          epf_number?: string | null
          file_number?: string | null
          full_name?: string | null
          gender?: string | null
          ic_number?: string | null
          id?: string
          income_tax_number?: string | null
          is_profile_complete?: boolean | null
          is_school_admin?: boolean | null
          options?: Json | null
          pensionable_position_date?: string | null
          position_confirmation_date?: string | null
          religion?: string | null
          retirement_date?: string | null
          role?: Json | null
          salary_number?: string | null
          spp_reference_number?: string | null
          teacher_type?: string | null
          teaching_days_mode?: string | null
          time_slots?: Json | null
          updated_at?: string | null
          username?: string | null
        }
        Relationships: []
      }
      reflection_templates: {
        Row: {
          category: string
          created_at: string
          created_by: string | null
          default_values: Json | null
          description: string | null
          id: string
          is_system_template: boolean | null
          name: string
          prompts: Json
          updated_at: string
          usage_count: number | null
        }
        Insert: {
          category: string
          created_at?: string
          created_by?: string | null
          default_values?: Json | null
          description?: string | null
          id?: string
          is_system_template?: boolean | null
          name: string
          prompts?: Json
          updated_at?: string
          usage_count?: number | null
        }
        Update: {
          category?: string
          created_at?: string
          created_by?: string | null
          default_values?: Json | null
          description?: string | null
          id?: string
          is_system_template?: boolean | null
          name?: string
          prompts?: Json
          updated_at?: string
          usage_count?: number | null
        }
        Relationships: []
      }
      rph_weeks: {
        Row: {
          created_at: string
          end_date: string | null
          id: string
          name: string
          start_date: string | null
          theme: string | null
          topic: string | null
          updated_at: string
          user_id: string
          week_number: number
        }
        Insert: {
          created_at?: string
          end_date?: string | null
          id?: string
          name: string
          start_date?: string | null
          theme?: string | null
          topic?: string | null
          updated_at?: string
          user_id: string
          week_number: number
        }
        Update: {
          created_at?: string
          end_date?: string | null
          id?: string
          name?: string
          start_date?: string | null
          theme?: string | null
          topic?: string | null
          updated_at?: string
          user_id?: string
          week_number?: number
        }
        Relationships: []
      }
      rpt_documents: {
        Row: {
          class_id: string
          class_name: string
          created_at: string | null
          file_mime_type: string
          file_name: string
          file_size_bytes: number
          id: string
          storage_file_path: string
          subject_id: string
          subject_name: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          class_id: string
          class_name: string
          created_at?: string | null
          file_mime_type: string
          file_name: string
          file_size_bytes: number
          id?: string
          storage_file_path: string
          subject_id: string
          subject_name: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          class_id?: string
          class_name?: string
          created_at?: string | null
          file_mime_type?: string
          file_name?: string
          file_size_bytes?: number
          id?: string
          storage_file_path?: string
          subject_id?: string
          subject_name?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "rpt_documents_subject_id_fkey"
            columns: ["subject_id"]
            isOneToOne: false
            referencedRelation: "subjects"
            referencedColumns: ["id"]
          },
        ]
      }
      school_memberships: {
        Row: {
          created_at: string | null
          id: string
          invitation_email: string | null
          invitation_expires_at: string | null
          invitation_token: string | null
          joined_at: string | null
          last_active: string | null
          notes: string | null
          role: string
          school_id: string | null
          status: string
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          invitation_email?: string | null
          invitation_expires_at?: string | null
          invitation_token?: string | null
          joined_at?: string | null
          last_active?: string | null
          notes?: string | null
          role?: string
          school_id?: string | null
          status?: string
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          invitation_email?: string | null
          invitation_expires_at?: string | null
          invitation_token?: string | null
          joined_at?: string | null
          last_active?: string | null
          notes?: string | null
          role?: string
          school_id?: string | null
          status?: string
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "school_memberships_school_id_fkey"
            columns: ["school_id"]
            isOneToOne: false
            referencedRelation: "schools"
            referencedColumns: ["id"]
          },
        ]
      }
      schools: {
        Row: {
          admin_user_id: string | null
          code: string
          created_at: string | null
          description: string | null
          established: number | null
          id: string
          is_active: boolean | null
          language: string | null
          last_tested: string | null
          location: string | null
          name: string
          next_billing_date: string | null
          response_time: string | null
          settings: Json | null
          subdomain_status: string | null
          subscription_plan: string | null
          subscription_status: string | null
          timezone: string | null
          total_revenue: number | null
          updated_at: string | null
        }
        Insert: {
          admin_user_id?: string | null
          code: string
          created_at?: string | null
          description?: string | null
          established?: number | null
          id?: string
          is_active?: boolean | null
          language?: string | null
          last_tested?: string | null
          location?: string | null
          name: string
          next_billing_date?: string | null
          response_time?: string | null
          settings?: Json | null
          subdomain_status?: string | null
          subscription_plan?: string | null
          subscription_status?: string | null
          timezone?: string | null
          total_revenue?: number | null
          updated_at?: string | null
        }
        Update: {
          admin_user_id?: string | null
          code?: string
          created_at?: string | null
          description?: string | null
          established?: number | null
          id?: string
          is_active?: boolean | null
          language?: string | null
          last_tested?: string | null
          location?: string | null
          name?: string
          next_billing_date?: string | null
          response_time?: string | null
          settings?: Json | null
          subdomain_status?: string | null
          subscription_plan?: string | null
          subscription_status?: string | null
          timezone?: string | null
          total_revenue?: number | null
          updated_at?: string | null
        }
        Relationships: []
      }
      subjects: {
        Row: {
          category: string | null
          code: string
          created_at: string
          id: string
          is_active: boolean | null
          is_custom: boolean
          level_type: string | null
          name: string
          school_id: string | null
          sort_order: number | null
          sub_level: string | null
          updated_at: string
          user_id: string | null
        }
        Insert: {
          category?: string | null
          code: string
          created_at?: string
          id?: string
          is_active?: boolean | null
          is_custom?: boolean
          level_type?: string | null
          name: string
          school_id?: string | null
          sort_order?: number | null
          sub_level?: string | null
          updated_at?: string
          user_id?: string | null
        }
        Update: {
          category?: string | null
          code?: string
          created_at?: string
          id?: string
          is_active?: boolean | null
          is_custom?: boolean
          level_type?: string | null
          name?: string
          school_id?: string | null
          sort_order?: number | null
          sub_level?: string | null
          updated_at?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "subjects_school_id_fkey"
            columns: ["school_id"]
            isOneToOne: false
            referencedRelation: "schools"
            referencedColumns: ["id"]
          },
        ]
      }
      teacher_activities: {
        Row: {
          activity_description: string
          category: string
          created_at: string
          end_date: string | null
          id: string
          is_active: boolean | null
          location: string | null
          notes: string | null
          school_id: string | null
          start_date: string | null
          updated_at: string
          user_id: string
        }
        Insert: {
          activity_description: string
          category: string
          created_at?: string
          end_date?: string | null
          id?: string
          is_active?: boolean | null
          location?: string | null
          notes?: string | null
          school_id?: string | null
          start_date?: string | null
          updated_at?: string
          user_id: string
        }
        Update: {
          activity_description?: string
          category?: string
          created_at?: string
          end_date?: string | null
          id?: string
          is_active?: boolean | null
          location?: string | null
          notes?: string | null
          school_id?: string | null
          start_date?: string | null
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "teacher_activities_school_id_fkey"
            columns: ["school_id"]
            isOneToOne: false
            referencedRelation: "schools"
            referencedColumns: ["id"]
          },
        ]
      }
      teacher_observer_assignments: {
        Row: {
          assigned_by: string | null
          created_at: string | null
          id: string
          is_active: boolean | null
          observer_id: string
          teacher_id: string
          updated_at: string | null
        }
        Insert: {
          assigned_by?: string | null
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          observer_id: string
          teacher_id: string
          updated_at?: string | null
        }
        Update: {
          assigned_by?: string | null
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          observer_id?: string
          teacher_id?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      teacher_schedules: {
        Row: {
          created_at: string
          id: string
          lesson_plan_id: string | null
          schedule_details: Json
          school_id: string | null
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          lesson_plan_id?: string | null
          schedule_details?: Json
          school_id?: string | null
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          lesson_plan_id?: string | null
          schedule_details?: Json
          school_id?: string | null
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "teacher_schedules_lesson_plan_fkey"
            columns: ["lesson_plan_id"]
            isOneToOne: false
            referencedRelation: "lesson_plans"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "teacher_schedules_school_id_fkey"
            columns: ["school_id"]
            isOneToOne: false
            referencedRelation: "schools"
            referencedColumns: ["id"]
          },
        ]
      }
      teacher_tasks: {
        Row: {
          category: string
          created_at: string
          due_date: string | null
          id: string
          is_completed: boolean | null
          notes: string | null
          priority: string | null
          task_description: string
          updated_at: string
          user_id: string
        }
        Insert: {
          category: string
          created_at?: string
          due_date?: string | null
          id?: string
          is_completed?: boolean | null
          notes?: string | null
          priority?: string | null
          task_description: string
          updated_at?: string
          user_id: string
        }
        Update: {
          category?: string
          created_at?: string
          due_date?: string | null
          id?: string
          is_completed?: boolean | null
          notes?: string | null
          priority?: string | null
          task_description?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      tidak_terlaksana: {
        Row: {
          created_at: string | null
          id: string
          is_default: boolean | null
          option_text: string
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          is_default?: boolean | null
          option_text: string
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          is_default?: boolean | null
          option_text?: string
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      timetable_entries: {
        Row: {
          activity_description: string | null
          activity_title: string | null
          activity_type: Database["public"]["Enums"]["activity_type_enum"]
          class_id: string | null
          class_name: string | null
          created_at: string
          day: string
          id: string
          notes: string | null
          room: string | null
          school_id: string | null
          subject_id: string | null
          subject_name: string | null
          teacher_schedule_created_at: string | null
          teacher_schedule_id: string | null
          time_slot_end: string
          time_slot_start: string
          updated_at: string
          user_id: string
        }
        Insert: {
          activity_description?: string | null
          activity_title?: string | null
          activity_type?: Database["public"]["Enums"]["activity_type_enum"]
          class_id?: string | null
          class_name?: string | null
          created_at?: string
          day: string
          id?: string
          notes?: string | null
          room?: string | null
          school_id?: string | null
          subject_id?: string | null
          subject_name?: string | null
          teacher_schedule_created_at?: string | null
          teacher_schedule_id?: string | null
          time_slot_end: string
          time_slot_start: string
          updated_at?: string
          user_id: string
        }
        Update: {
          activity_description?: string | null
          activity_title?: string | null
          activity_type?: Database["public"]["Enums"]["activity_type_enum"]
          class_id?: string | null
          class_name?: string | null
          created_at?: string
          day?: string
          id?: string
          notes?: string | null
          room?: string | null
          school_id?: string | null
          subject_id?: string | null
          subject_name?: string | null
          teacher_schedule_created_at?: string | null
          teacher_schedule_id?: string | null
          time_slot_end?: string
          time_slot_start?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "timetable_entries_school_id_fkey"
            columns: ["school_id"]
            isOneToOne: false
            referencedRelation: "schools"
            referencedColumns: ["id"]
          },
        ]
      }
      tindakan_susulan: {
        Row: {
          created_at: string | null
          id: string
          is_default: boolean | null
          option_text: string
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          is_default?: boolean | null
          option_text: string
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          is_default?: boolean | null
          option_text?: string
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      user_preferences: {
        Row: {
          created_at: string | null
          id: string
          notification_settings: Json | null
          onboarding_state: Json | null
          template_preferences: Json | null
          ui_preferences: Json | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          notification_settings?: Json | null
          onboarding_state?: Json | null
          template_preferences?: Json | null
          ui_preferences?: Json | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          notification_settings?: Json | null
          onboarding_state?: Json | null
          template_preferences?: Json | null
          ui_preferences?: Json | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      user_reflection_template_preferences: {
        Row: {
          created_at: string
          customizations: Json | null
          id: string
          is_favorite: boolean | null
          last_used: string | null
          template_id: string
          updated_at: string
          usage_count: number | null
          user_id: string
        }
        Insert: {
          created_at?: string
          customizations?: Json | null
          id?: string
          is_favorite?: boolean | null
          last_used?: string | null
          template_id: string
          updated_at?: string
          usage_count?: number | null
          user_id: string
        }
        Update: {
          created_at?: string
          customizations?: Json | null
          id?: string
          is_favorite?: boolean | null
          last_used?: string | null
          template_id?: string
          updated_at?: string
          usage_count?: number | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_reflection_template_preferences_template_id_fkey"
            columns: ["template_id"]
            isOneToOne: false
            referencedRelation: "reflection_templates"
            referencedColumns: ["id"]
          },
        ]
      }
      user_week_submissions: {
        Row: {
          created_at: string
          id: string
          reviewed_at: string | null
          reviewer_id: string | null
          status: Database["public"]["Enums"]["submission_status_enum"]
          submitted_at: string | null
          supervisor_comments: string | null
          updated_at: string
          user_id: string
          week_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          reviewed_at?: string | null
          reviewer_id?: string | null
          status?: Database["public"]["Enums"]["submission_status_enum"]
          submitted_at?: string | null
          supervisor_comments?: string | null
          updated_at?: string
          user_id: string
          week_id: string
        }
        Update: {
          created_at?: string
          id?: string
          reviewed_at?: string | null
          reviewer_id?: string | null
          status?: Database["public"]["Enums"]["submission_status_enum"]
          submitted_at?: string | null
          supervisor_comments?: string | null
          updated_at?: string
          user_id?: string
          week_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_week_submissions_week_id_fkey"
            columns: ["week_id"]
            isOneToOne: false
            referencedRelation: "rph_weeks"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      calculate_overall_rating_from_jsonb: {
        Args: { reflections_data: Json }
        Returns: number
      }
      delete_user_by_id: {
        Args: { user_id: number } | { user_id: string }
        Returns: undefined
      }
      get_tidak_terlaksana_details: {
        Args: { reflection_id: string }
        Returns: {
          id: string
          option_text: string
          is_default: boolean
        }[]
      }
      get_tindakan_susulan_details: {
        Args: { reflection_id: string }
        Returns: {
          id: string
          option_text: string
          is_default: boolean
        }[]
      }
      get_user_template_preferences: {
        Args: { p_user_id: string }
        Returns: Json
      }
      update_user_template_preferences: {
        Args: { p_user_id: string; p_preferences: Json }
        Returns: Json
      }
      validate_schedule_details: {
        Args: { details: Json }
        Returns: boolean
      }
      validate_tindakan_susulan_array: {
        Args: { tindakan_array: Json }
        Returns: boolean
      }
    }
    Enums: {
      activity_type_enum:
        | "CLASS"
        | "ASSEMBLY"
        | "COCURRICULAR"
        | "MEETING"
        | "BREAK"
        | "OTHER"
      submission_status_enum: "Draf" | "Dihantar" | "Disemak" | "Ditolak"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      activity_type_enum: [
        "CLASS",
        "ASSEMBLY",
        "COCURRICULAR",
        "MEETING",
        "BREAK",
        "OTHER",
      ],
      submission_status_enum: ["Draf", "Dihantar", "Disemak", "Ditolak"],
    },
  },
} as const
