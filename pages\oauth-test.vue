<template>
    <div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div class="sm:mx-auto sm:w-full sm:max-w-md">
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                OAuth Test Page
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600">
                Test Google OAuth flow with debugging
            </p>
        </div>

        <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
            <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">

                <!-- Current Info -->
                <div class="mb-6 p-4 bg-gray-100 rounded-lg">
                    <h3 class="text-sm font-medium text-gray-900 mb-2">Current Environment:</h3>
                    <div class="text-xs text-gray-600 space-y-1">
                        <div>Host: {{ currentHost }}</div>
                        <div>School Code: {{ schoolCode }}</div>
                        <div>Stored Code: {{ storedSchoolCode }}</div>
                        <div>Redirect URL: {{ redirectUrl }}</div>
                    </div>
                </div>

                <!-- Test Button -->
                <div class="space-y-4">
                    <button @click="testGoogleOAuth" :disabled="loading"
                        class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50">
                        <span v-if="!loading">Test Google OAuth</span>
                        <span v-else>Initiating OAuth...</span>
                    </button>

                    <button @click="clearStorage"
                        class="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Clear localStorage
                    </button>
                </div>

                <!-- Messages -->
                <div v-if="message" class="mt-4 p-3 rounded-md" :class="messageClass">
                    <div class="text-sm">{{ message }}</div>
                </div>

                <!-- Instructions -->
                <div class="mt-6 p-4 bg-blue-50 rounded-lg">
                    <h3 class="text-sm font-medium text-blue-900 mb-2">Instructions:</h3>
                    <ol class="text-xs text-blue-800 space-y-1 list-decimal list-inside">
                        <li>Ensure you're on a subdomain (e.g., test.localhost:3000)</li>
                        <li>Add http://localhost:3000/auth/callback to Supabase redirect URLs</li>
                        <li>Click "Test Google OAuth" and check console logs</li>
                        <li>Verify school code is stored in localStorage</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
const supabase = useSupabaseClient();

const loading = ref(false);
const message = ref('');
const messageType = ref<'success' | 'error' | 'info'>('info');

const currentHost = ref('');
const schoolCode = ref('');
const storedSchoolCode = ref('');
const redirectUrl = ref('');

onMounted(() => {
    if (process.client) {
        currentHost.value = window.location.host;
        schoolCode.value = window.location.host.split('.')[0];
        storedSchoolCode.value = localStorage.getItem('oauth_school_code') || 'None';
        redirectUrl.value = 'http://localhost:3000/auth/callback';

        console.log('[oauth-test] Page loaded');
        console.log('[oauth-test] Host:', currentHost.value);
        console.log('[oauth-test] School code:', schoolCode.value);
        console.log('[oauth-test] Stored school code:', storedSchoolCode.value);

        // Auto-refresh stored school code display (only on client)
        const intervalId = setInterval(() => {
            storedSchoolCode.value = localStorage.getItem('oauth_school_code') || 'None';
        }, 1000);

        // Clean up interval on unmount
        onUnmounted(() => {
            clearInterval(intervalId);
        });
    }
});

const messageClass = computed(() => {
    switch (messageType.value) {
        case 'success': return 'bg-green-50 border border-green-200 text-green-800';
        case 'error': return 'bg-red-50 border border-red-200 text-red-800';
        default: return 'bg-blue-50 border border-blue-200 text-blue-800';
    }
});

const testGoogleOAuth = async () => {
    if (!process.client) return;

    loading.value = true;
    message.value = '';

    try {
        const currentHost = window.location.host;
        const extractedSchoolCode = currentHost.split('.')[0];

        console.log('[oauth-test] Starting OAuth test...');
        console.log('[oauth-test] Current host:', currentHost);
        console.log('[oauth-test] Extracted school code:', extractedSchoolCode);

        // Store school code
        if (extractedSchoolCode && extractedSchoolCode !== 'localhost') {
            localStorage.setItem('oauth_school_code', extractedSchoolCode);
            console.log('[oauth-test] Stored school code:', extractedSchoolCode);

            // Verify storage
            const verified = localStorage.getItem('oauth_school_code');
            console.log('[oauth-test] Verified stored code:', verified);

            if (verified === extractedSchoolCode) {
                message.value = `School code "${extractedSchoolCode}" stored successfully. Starting OAuth...`;
                messageType.value = 'success';
            } else {
                message.value = 'Failed to store school code!';
                messageType.value = 'error';
                loading.value = false;
                return;
            }
        } else {
            message.value = 'Warning: No valid school code detected. Are you on a subdomain?';
            messageType.value = 'error';
            console.warn('[oauth-test] Invalid school code:', extractedSchoolCode);
        }

        // Start OAuth flow
        setTimeout(async () => {
            // Try Supabase's callback URL directly first
            const redirectUrl = 'https://nhgyywlfopodxomxbegx.supabase.co/auth/v1/callback';
            console.log('[oauth-test] Using Supabase callback URL:', redirectUrl);

            const { error } = await supabase.auth.signInWithOAuth({
                provider: 'google',
                options: {
                    redirectTo: redirectUrl,
                    queryParams: {
                        prompt: 'select_account',
                    }
                },
            });

            if (error) {
                console.error('[oauth-test] Supabase callback failed:', error);

                // Try our localhost callback as fallback
                console.log('[oauth-test] Trying localhost callback...');
                const localRedirectUrl = 'http://localhost:3000/auth/callback';

                const { error: error2 } = await supabase.auth.signInWithOAuth({
                    provider: 'google',
                    options: {
                        redirectTo: localRedirectUrl,
                        queryParams: {
                            prompt: 'select_account',
                        }
                    },
                });

                if (error2) {
                    console.error('[oauth-test] Both OAuth methods failed:', error, error2);
                    message.value = `OAuth failed: ${error.message}. Localhost also failed: ${error2.message}`;
                    messageType.value = 'error';
                    loading.value = false;
                } else {
                    console.log('[oauth-test] Localhost OAuth initiated successfully');
                }
            } else {
                console.log('[oauth-test] Supabase OAuth initiated successfully');
                // User will be redirected
            }
        }, 1000);

    } catch (error: any) {
        console.error('[oauth-test] Test error:', error);
        message.value = `Test error: ${error.message}`;
        messageType.value = 'error';
        loading.value = false;
    }
};

const clearStorage = () => {
    if (!process.client) return;

    localStorage.removeItem('oauth_school_code');
    storedSchoolCode.value = 'None';
    message.value = 'localStorage cleared';
    messageType.value = 'info';
    console.log('[oauth-test] localStorage cleared');
};
</script>
